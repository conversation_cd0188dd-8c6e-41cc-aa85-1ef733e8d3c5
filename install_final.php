<?php
/**
 * ملف التثبيت النهائي - نظام حكيم لإدارة العيادات
 * Final Installation File - Hakim Clinic Management System
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'collectandwin2_altarda';
$db_user = 'collectandwin2_altarda';
$db_pass = 'collectandwin2_altarda';

$message = '';
$error = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    try {
        // الاتصال بقاعدة البيانات الموجودة
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء الجداول مباشرة بدلاً من قراءة ملف SQL
        
        // جدول المستخدمين
        $pdo->exec("CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `user_type` enum('admin','doctor','secretary') NOT NULL DEFAULT 'secretary',
            `specialization` varchar(100) DEFAULT NULL,
            `license_number` varchar(50) DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `last_login` datetime DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_username` (`username`),
            KEY `idx_user_type` (`user_type`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول المرضى
        $pdo->exec("CREATE TABLE IF NOT EXISTS `patients` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `patient_number` varchar(20) NOT NULL UNIQUE,
            `first_name` varchar(50) NOT NULL,
            `last_name` varchar(50) NOT NULL,
            `date_of_birth` date DEFAULT NULL,
            `gender` enum('male','female') NOT NULL,
            `phone` varchar(20) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `national_id` varchar(20) DEFAULT NULL,
            `emergency_contact_name` varchar(100) DEFAULT NULL,
            `emergency_contact_phone` varchar(20) DEFAULT NULL,
            `blood_type` varchar(5) DEFAULT NULL,
            `allergies` text DEFAULT NULL,
            `chronic_diseases` text DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `patient_number` (`patient_number`),
            KEY `idx_name` (`first_name`, `last_name`),
            KEY `idx_phone` (`phone`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول المواعيد
        $pdo->exec("CREATE TABLE IF NOT EXISTS `appointments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `patient_id` int(11) NOT NULL,
            `doctor_id` int(11) NOT NULL,
            `appointment_date` date NOT NULL,
            `appointment_time` time NOT NULL,
            `duration` int(11) NOT NULL DEFAULT 30,
            `reason` varchar(255) DEFAULT NULL,
            `status` enum('scheduled','confirmed','completed','cancelled','no_show') NOT NULL DEFAULT 'scheduled',
            `notes` text DEFAULT NULL,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_patient_id` (`patient_id`),
            KEY `idx_doctor_id` (`doctor_id`),
            KEY `idx_appointment_date` (`appointment_date`),
            KEY `idx_status` (`status`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول الوصفات الطبية
        $pdo->exec("CREATE TABLE IF NOT EXISTS `prescriptions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `patient_id` int(11) NOT NULL,
            `doctor_id` int(11) NOT NULL,
            `visit_id` int(11) DEFAULT NULL,
            `prescription_date` date NOT NULL,
            `notes` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_patient_id` (`patient_id`),
            KEY `idx_doctor_id` (`doctor_id`),
            KEY `idx_prescription_date` (`prescription_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول تفاصيل الوصفات
        $pdo->exec("CREATE TABLE IF NOT EXISTS `prescription_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `prescription_id` int(11) NOT NULL,
            `medication_id` int(11) DEFAULT NULL,
            `medication_name` varchar(255) NOT NULL,
            `dosage` varchar(100) NOT NULL,
            `frequency` varchar(100) NOT NULL,
            `duration` varchar(100) NOT NULL,
            `instructions` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_prescription_id` (`prescription_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول الفواتير
        $pdo->exec("CREATE TABLE IF NOT EXISTS `billing` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `patient_id` int(11) NOT NULL,
            `visit_id` int(11) DEFAULT NULL,
            `appointment_id` int(11) DEFAULT NULL,
            `invoice_number` varchar(50) NOT NULL UNIQUE,
            `invoice_date` date NOT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `status` enum('pending','partial','paid','cancelled') NOT NULL DEFAULT 'pending',
            `payment_method` varchar(50) DEFAULT NULL,
            `payment_date` datetime DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `invoice_number` (`invoice_number`),
            KEY `idx_patient_id` (`patient_id`),
            KEY `idx_invoice_date` (`invoice_date`),
            KEY `idx_status` (`status`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول تفاصيل الفواتير
        $pdo->exec("CREATE TABLE IF NOT EXISTS `billing_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `billing_id` int(11) NOT NULL,
            `service_id` int(11) DEFAULT NULL,
            `service_name` varchar(255) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 1,
            `unit_price` decimal(10,2) NOT NULL,
            `total_price` decimal(10,2) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_billing_id` (`billing_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول الخدمات
        $pdo->exec("CREATE TABLE IF NOT EXISTS `services` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `category` varchar(100) DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_category` (`category`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول الأدوية
        $pdo->exec("CREATE TABLE IF NOT EXISTS `medications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `generic_name` varchar(255) DEFAULT NULL,
            `strength` varchar(100) DEFAULT NULL,
            `form` varchar(100) DEFAULT NULL,
            `manufacturer` varchar(255) DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_generic_name` (`generic_name`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول سجل النشاط
        $pdo->exec("CREATE TABLE IF NOT EXISTS `activity_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `action` varchar(255) NOT NULL,
            `table_name` varchar(100) DEFAULT NULL,
            `record_id` int(11) DEFAULT NULL,
            `old_data` json DEFAULT NULL,
            `new_data` json DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_action` (`action`),
            KEY `idx_table_name` (`table_name`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // إدراج المستخدمين الافتراضيين
        $stmt = $pdo->prepare("INSERT IGNORE INTO `users` (`username`, `password`, `full_name`, `email`, `phone`, `user_type`, `specialization`, `license_number`, `is_active`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $users = [
            ['admin', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'مدير النظام', '<EMAIL>', '**********', 'admin', null, null, 1],
            ['doctor', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'د. أحمد محمد', '<EMAIL>', '**********', 'doctor', 'طب عام', 'DOC001', 1],
            ['secretary', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'فاطمة أحمد', '<EMAIL>', '**********', 'secretary', null, null, 1]
        ];
        
        foreach ($users as $user) {
            $stmt->execute($user);
        }
        
        // إدراج الخدمات الأساسية
        $stmt = $pdo->prepare("INSERT IGNORE INTO `services` (`name`, `description`, `price`, `category`, `is_active`) VALUES (?, ?, ?, ?, ?)");
        
        $services = [
            ['كشف عام', 'فحص طبي عام', 100.00, 'فحوصات', 1],
            ['استشارة طبية', 'استشارة طبية متخصصة', 150.00, 'استشارات', 1],
            ['تحليل دم شامل', 'فحص دم شامل', 80.00, 'تحاليل', 1],
            ['أشعة سينية', 'تصوير بالأشعة السينية', 120.00, 'أشعة', 1],
            ['تخطيط قلب', 'رسم قلب كهربائي', 90.00, 'فحوصات', 1]
        ];
        
        foreach ($services as $service) {
            $stmt->execute($service);
        }
        
        // إدراج الأدوية الأساسية
        $stmt = $pdo->prepare("INSERT IGNORE INTO `medications` (`name`, `generic_name`, `strength`, `form`, `manufacturer`, `is_active`) VALUES (?, ?, ?, ?, ?, ?)");
        
        $medications = [
            ['بنادول', 'باراسيتامول', '500mg', 'أقراص', 'GSK', 1],
            ['بروفين', 'إيبوبروفين', '400mg', 'أقراص', 'Abbott', 1],
            ['أوجمنتين', 'أموكسيسيلين + كلافولانيك', '625mg', 'أقراص', 'GSK', 1],
            ['فولتارين', 'ديكلوفيناك', '50mg', 'أقراص', 'Novartis', 1],
            ['زيرتك', 'سيتيريزين', '10mg', 'أقراص', 'UCB', 1]
        ];
        
        foreach ($medications as $medication) {
            $stmt->execute($medication);
        }
        
        $message = 'تم تثبيت النظام بنجاح! يمكنك الآن تسجيل الدخول.';
        $success = true;
        
    } catch (Exception $e) {
        $error = 'خطأ في التثبيت: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام حكيم النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #007BFF, #0056b3); min-height: 100vh; }
        .install-container { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); padding: 3rem; max-width: 600px; margin: 2rem auto; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="text-center mb-4">
            <i class="fas fa-stethoscope fa-3x text-primary mb-3"></i>
            <h1 class="text-primary">تثبيت نظام حكيم</h1>
            <p class="text-muted">التثبيت النهائي المحسن</p>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success text-center">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!$success): ?>
            <div class="mb-4">
                <h5>إعدادات قاعدة البيانات:</h5>
                <div class="bg-light p-3 rounded">
                    <p><strong>الخادم:</strong> <?php echo $db_host; ?></p>
                    <p><strong>قاعدة البيانات:</strong> <?php echo $db_name; ?></p>
                    <p><strong>المستخدم:</strong> <?php echo $db_user; ?></p>
                    <p class="mb-0"><strong>كلمة المرور:</strong> ***مخفية***</p>
                </div>
            </div>
            
            <form method="POST">
                <div class="d-grid">
                    <button type="submit" name="install" class="btn btn-primary btn-lg">
                        <i class="fas fa-download me-2"></i>بدء التثبيت النهائي
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div class="mb-4">
                <h5>بيانات تسجيل الدخول:</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                <h6>مدير</h6>
                                <small>admin<br>admin123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                                <h6>طبيب</h6>
                                <small>doctor<br>doctor123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                <h6>سكرتيرة</h6>
                                <small>secretary<br>secretary123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <a href="login.php" class="btn btn-success btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </a>
                <a href="index.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                </a>
            </div>
            
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>مهم:</strong> احذف جميع ملفات التثبيت بعد اكتمال العملية لأسباب أمنية.
            </div>
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <small class="text-muted">نظام حكيم لإدارة العيادات - الإصدار 1.0.0</small>
        </div>
    </div>
</body>
</html>
