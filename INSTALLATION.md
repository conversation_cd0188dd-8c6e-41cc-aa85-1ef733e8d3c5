# دليل التثبيت - نظام حكيم لإدارة العيادات

## 📋 المتطلبات التقنية

### متطلبات الخادم
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache**: 2.4+ مع mod_rewrite
- **مساحة القرص**: 100 ميجابايت على الأقل
- **الذاكرة**: 256 ميجابايت RAM على الأقل

### امتدادات PHP المطلوبة
- PDO
- PDO_MySQL
- mbstring
- openssl
- json
- fileinfo
- gd (للتعامل مع الصور)

## 🚀 خطوات التثبيت

### 1. تحضير الملفات
```bash
# تحميل الملفات إلى خادم الويب
# رفع جميع الملفات إلى المجلد الجذر للموقع
```

### 2. إعد<PERSON> قاعدة البيانات

#### إنشاء قاعدة البيانات
```sql
CREATE DATABASE hakim_clinic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### إنشاء مستخدم قاعدة البيانات
```sql
CREATE USER 'hakim_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON hakim_clinic.* TO 'hakim_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تحديث إعدادات قاعدة البيانات
قم بتحرير الملف `config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hakim_clinic');
define('DB_USER', 'hakim_user');
define('DB_PASS', 'your_password_here');
```

### 4. تشغيل التثبيت
1. افتح المتصفح واذهب إلى: `http://yoursite.com/install.php`
2. اتبع التعليمات على الشاشة
3. انقر على "بدء التثبيت"

### 5. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chmod 755 logs/ (إذا كان موجوداً)

# التأكد من أن Apache يمكنه قراءة الملفات
chown -R www-data:www-data /path/to/hakim/
```

### 6. إعدادات الأمان
- احذف ملف `install.php` بعد التثبيت
- غير كلمات المرور الافتراضية
- تأكد من تفعيل HTTPS في الإنتاج

## 🔐 بيانات تسجيل الدخول الافتراضية

### مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: إدارة كاملة للنظام

### طبيب تجريبي
- **اسم المستخدم**: `doctor`
- **كلمة المرور**: `doctor123`
- **الصلاحيات**: إدارة المرضى والمواعيد والوصفات

### سكرتيرة تجريبية
- **اسم المستخدم**: `secretary`
- **كلمة المرور**: `secretary123`
- **الصلاحيات**: إدارة المواعيد والمرضى (عرض فقط)

## ⚙️ إعدادات إضافية

### إعداد البريد الإلكتروني
قم بتحديث الإعدادات في `config/config.php`:

```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
```

### إعداد SMS (اختياري)
```php
define('SMS_PROVIDER', 'your-sms-provider');
define('SMS_API_KEY', 'your-api-key');
define('SMS_SENDER_NAME', 'YourClinic');
```

### إعداد النسخ الاحتياطي التلقائي
```bash
# إضافة مهمة cron للنسخ الاحتياطي اليومي
0 2 * * * /usr/bin/mysqldump -u hakim_user -p'password' hakim_clinic > /backup/hakim_$(date +\%Y\%m\%d).sql
```

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
خطأ: SQLSTATE[HY000] [1045] Access denied
```
**الحل**: تحقق من بيانات الاتصال في `config/database.php`

#### خطأ في صلاحيات الملفات
```
خطأ: Permission denied
```
**الحل**: 
```bash
chmod 755 uploads/
chown www-data:www-data uploads/
```

#### خطأ في mod_rewrite
```
خطأ: Internal Server Error
```
**الحل**: تأكد من تفعيل mod_rewrite في Apache

#### مشكلة في عرض الخط العربي
**الحل**: تأكد من أن قاعدة البيانات تستخدم `utf8mb4_unicode_ci`

### تفعيل وضع التصحيح
في `config/config.php`:
```php
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
```

## 📊 مراقبة الأداء

### إعدادات PHP الموصى بها
```ini
memory_limit = 256M
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
max_input_vars = 3000
```

### إعدادات MySQL الموصى بها
```ini
innodb_buffer_pool_size = 128M
max_connections = 100
query_cache_size = 32M
```

## 🔄 التحديث

### تحديث النظام
1. عمل نسخة احتياطية من قاعدة البيانات والملفات
2. رفع الملفات الجديدة
3. تشغيل سكريبت التحديث (إذا كان متوفراً)
4. مسح الكاش

### النسخ الاحتياطي
```bash
# نسخة احتياطية من قاعدة البيانات
mysqldump -u hakim_user -p hakim_clinic > backup_$(date +%Y%m%d).sql

# نسخة احتياطية من الملفات
tar -czf hakim_files_$(date +%Y%m%d).tar.gz /path/to/hakim/
```

## 📞 الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966501234567
- **الموقع**: https://hakimclinic.com

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- وصف تفصيلي للمشكلة
- خطوات إعادة إنتاج المشكلة
- رسائل الخطأ (إن وجدت)
- معلومات النظام (PHP، MySQL، Apache)

## 📝 ملاحظات مهمة

### الأمان
- غير كلمات المرور الافتراضية فوراً
- استخدم HTTPS في الإنتاج
- قم بتحديث النظام بانتظام
- راجع سجلات النشاط دورياً

### الأداء
- استخدم CDN للملفات الثابتة
- فعل ضغط gzip
- استخدم التخزين المؤقت
- راقب استخدام الموارد

### النسخ الاحتياطي
- اعمل نسخة احتياطية يومية
- اختبر استعادة النسخ الاحتياطية
- احتفظ بنسخ متعددة
- خزن النسخ في مكان آمن

---

**تم إنشاء هذا الدليل بواسطة**: فريق تطوير نظام حكيم  
**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
