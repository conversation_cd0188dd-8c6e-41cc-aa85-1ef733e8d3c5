<?php
/**
 * إعدادات قاعدة البيانات - نظام حكيم لإدارة العيادات
 * Database Configuration - Hakim Clinic Management System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'collectandwin2_altarda');
define('DB_USER', 'collectandwin2_altarda');
define('DB_PASS', 'collectandwin2_altarda');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الاتصال
$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;

try {
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

/**
 * دالة للحصول على اتصال قاعدة البيانات
 * @return PDO
 */
function getDBConnection() {
    global $pdo;
    return $pdo;
}

/**
 * دالة لتنفيذ استعلام مع معاملات آمنة
 * @param string $query
 * @param array $params
 * @return PDOStatement
 */
function executeQuery($query, $params = []) {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    return $stmt;
}

/**
 * دالة للحصول على سجل واحد
 * @param string $query
 * @param array $params
 * @return array|false
 */
function fetchOne($query, $params = []) {
    $stmt = executeQuery($query, $params);
    return $stmt->fetch();
}

/**
 * دالة للحصول على عدة سجلات
 * @param string $query
 * @param array $params
 * @return array
 */
function fetchAll($query, $params = []) {
    $stmt = executeQuery($query, $params);
    return $stmt->fetchAll();
}

/**
 * دالة للحصول على آخر ID مُدرج
 * @return string
 */
function getLastInsertId() {
    $pdo = getDBConnection();
    return $pdo->lastInsertId();
}
?>
