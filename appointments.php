<?php
/**
 * إدارة المواعيد - نظام حكيم لإدارة العيادات
 * Appointments Management - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات
if (!hasPermission('secretary')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    redirect('dashboard.php');
}

$pageTitle = 'إدارة المواعيد';
$action = $_GET['action'] ?? 'list';
$appointmentId = $_GET['id'] ?? null;
$selectedDate = $_GET['date'] ?? date('Y-m-d');

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $data = [
            'patient_id' => intval($_POST['patient_id']),
            'doctor_id' => intval($_POST['doctor_id']),
            'appointment_date' => $_POST['appointment_date'],
            'appointment_time' => $_POST['appointment_time'],
            'duration' => intval($_POST['duration'] ?? 30),
            'reason' => sanitizeInput($_POST['reason']),
            'notes' => sanitizeInput($_POST['notes'])
        ];
        
        // التحقق من صحة البيانات
        $errors = [];
        if (empty($data['patient_id'])) $errors[] = 'يجب اختيار المريض';
        if (empty($data['doctor_id'])) $errors[] = 'يجب اختيار الطبيب';
        if (empty($data['appointment_date'])) $errors[] = 'تاريخ الموعد مطلوب';
        if (empty($data['appointment_time'])) $errors[] = 'وقت الموعد مطلوب';
        
        // التحقق من تعارض المواعيد
        if (empty($errors)) {
            $conflictId = $action === 'edit' ? $appointmentId : null;
            if (checkAppointmentConflict($data['doctor_id'], $data['appointment_date'], $data['appointment_time'], $data['duration'], $conflictId)) {
                $errors[] = 'يوجد تعارض مع موعد آخر في نفس الوقت';
            }
        }
        
        if (empty($errors)) {
            try {
                if ($action === 'add') {
                    // إضافة موعد جديد
                    $data['created_by'] = $_SESSION['user_id'];
                    $data['status'] = 'scheduled';
                    
                    $query = "INSERT INTO appointments (patient_id, doctor_id, appointment_date, appointment_time, duration, reason, notes, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $params = [
                        $data['patient_id'], $data['doctor_id'], $data['appointment_date'],
                        $data['appointment_time'], $data['duration'], $data['reason'],
                        $data['notes'], $data['status'], $data['created_by']
                    ];
                    
                    executeQuery($query, $params);
                    $newAppointmentId = getLastInsertId();
                    
                    logActivity($_SESSION['user_id'], 'إضافة موعد جديد', 'appointments', $newAppointmentId, null, $data);
                    $_SESSION['success_message'] = 'تم حجز الموعد بنجاح';
                    
                } else {
                    // تحديث موعد موجود
                    $query = "UPDATE appointments SET patient_id=?, doctor_id=?, appointment_date=?, appointment_time=?, duration=?, reason=?, notes=? WHERE id=?";
                    
                    $params = [
                        $data['patient_id'], $data['doctor_id'], $data['appointment_date'],
                        $data['appointment_time'], $data['duration'], $data['reason'],
                        $data['notes'], $appointmentId
                    ];
                    
                    executeQuery($query, $params);
                    
                    logActivity($_SESSION['user_id'], 'تحديث موعد', 'appointments', $appointmentId, null, $data);
                    $_SESSION['success_message'] = 'تم تحديث الموعد بنجاح';
                }
                
                redirect('appointments.php?date=' . $data['appointment_date']);
                
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    }
}

// تحديث حالة الموعد
if ($action === 'update_status' && $appointmentId && isset($_GET['status'])) {
    $newStatus = $_GET['status'];
    $allowedStatuses = ['scheduled', 'confirmed', 'completed', 'cancelled', 'no_show'];
    
    if (in_array($newStatus, $allowedStatuses)) {
        try {
            $query = "UPDATE appointments SET status = ? WHERE id = ?";
            executeQuery($query, [$newStatus, $appointmentId]);
            
            logActivity($_SESSION['user_id'], 'تحديث حالة موعد', 'appointments', $appointmentId, null, ['status' => $newStatus]);
            $_SESSION['success_message'] = 'تم تحديث حالة الموعد بنجاح';
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'حدث خطأ في التحديث: ' . $e->getMessage();
        }
    }
    
    redirect('appointments.php?date=' . $selectedDate);
}

// حذف موعد
if ($action === 'delete' && $appointmentId) {
    try {
        $query = "DELETE FROM appointments WHERE id = ?";
        executeQuery($query, [$appointmentId]);
        
        logActivity($_SESSION['user_id'], 'حذف موعد', 'appointments', $appointmentId);
        $_SESSION['success_message'] = 'تم حذف الموعد بنجاح';
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ في الحذف: ' . $e->getMessage();
    }
    
    redirect('appointments.php?date=' . $selectedDate);
}

// جلب بيانات الموعد للتحرير
$appointment = null;
if ($action === 'edit' && $appointmentId) {
    $query = "SELECT * FROM appointments WHERE id = ?";
    $appointment = fetchOne($query, [$appointmentId]);
    
    if (!$appointment) {
        $_SESSION['error_message'] = 'الموعد غير موجود';
        redirect('appointments.php');
    }
}

// جلب قائمة الأطباء
$doctorsQuery = "SELECT id, full_name, specialization FROM users WHERE user_type = 'doctor' AND is_active = 1 ORDER BY full_name";
$doctors = fetchAll($doctorsQuery);

// جلب قائمة المرضى
$patientsQuery = "SELECT id, CONCAT(first_name, ' ', last_name) as full_name, phone, patient_number FROM patients ORDER BY first_name";
$patients = fetchAll($patientsQuery);

// جلب مواعيد اليوم المحدد
$appointmentsQuery = "
    SELECT a.*, 
           CONCAT(p.first_name, ' ', p.last_name) as patient_name,
           p.phone as patient_phone,
           p.patient_number,
           u.full_name as doctor_name,
           u.specialization
    FROM appointments a
    JOIN patients p ON a.patient_id = p.id
    JOIN users u ON a.doctor_id = u.id
    WHERE a.appointment_date = ?
    ORDER BY a.appointment_time
";
$todayAppointments = fetchAll($appointmentsQuery, [$selectedDate]);

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- عرض المواعيد -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-calendar-alt me-2"></i>إدارة المواعيد</h2>
    <a href="appointments.php?action=add&date=<?php echo $selectedDate; ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>حجز موعد جديد
    </a>
</div>

<!-- تقويم التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <a href="appointments.php?date=<?php echo date('Y-m-d', strtotime($selectedDate . ' -1 day')); ?>" 
                       class="btn btn-outline-primary me-2">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <h5 class="mb-0 mx-3">
                        <?php echo formatDateForDisplay($selectedDate, 'l، d F Y'); ?>
                    </h5>
                    <a href="appointments.php?date=<?php echo date('Y-m-d', strtotime($selectedDate . ' +1 day')); ?>" 
                       class="btn btn-outline-primary ms-2">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="appointments.php?date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-calendar-day me-1"></i>اليوم
                </a>
                <input type="date" class="form-control d-inline-block" style="width: auto;" 
                       value="<?php echo $selectedDate; ?>" 
                       onchange="window.location.href='appointments.php?date='+this.value">
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات اليوم -->
<div class="row mb-4">
    <?php
    $totalToday = count($todayAppointments);
    $completedToday = count(array_filter($todayAppointments, fn($a) => $a['status'] === 'completed'));
    $pendingToday = count(array_filter($todayAppointments, fn($a) => in_array($a['status'], ['scheduled', 'confirmed'])));
    $cancelledToday = count(array_filter($todayAppointments, fn($a) => in_array($a['status'], ['cancelled', 'no_show'])));
    ?>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?php echo $totalToday; ?></h3>
                <p class="mb-0">إجمالي المواعيد</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success"><?php echo $completedToday; ?></h3>
                <p class="mb-0">مكتملة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning"><?php echo $pendingToday; ?></h3>
                <p class="mb-0">معلقة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-danger"><?php echo $cancelledToday; ?></h3>
                <p class="mb-0">ملغية</p>
            </div>
        </div>
    </div>
</div>

<!-- قائمة المواعيد -->
<div class="card">
    <div class="card-body">
        <?php if (empty($todayAppointments)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواعيد في هذا اليوم</h5>
                <p class="text-muted">ابدأ بحجز موعد جديد</p>
                <a href="appointments.php?action=add&date=<?php echo $selectedDate; ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>حجز موعد جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>المريض</th>
                            <th>الطبيب</th>
                            <th>السبب</th>
                            <th>المدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($todayAppointments as $apt): ?>
                        <tr>
                            <td>
                                <strong><?php echo date('H:i', strtotime($apt['appointment_time'])); ?></strong>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($apt['patient_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($apt['patient_number']); ?> | 
                                        <a href="tel:<?php echo $apt['patient_phone']; ?>"><?php echo $apt['patient_phone']; ?></a>
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <?php echo htmlspecialchars($apt['doctor_name']); ?>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars($apt['specialization']); ?></small>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($apt['reason']); ?></td>
                            <td><?php echo $apt['duration']; ?> دقيقة</td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'scheduled' => 'warning',
                                    'confirmed' => 'info',
                                    'completed' => 'success',
                                    'cancelled' => 'danger',
                                    'no_show' => 'secondary'
                                ];
                                $statusTexts = [
                                    'scheduled' => 'مجدول',
                                    'confirmed' => 'مؤكد',
                                    'completed' => 'مكتمل',
                                    'cancelled' => 'ملغي',
                                    'no_show' => 'لم يحضر'
                                ];
                                $statusClass = $statusClasses[$apt['status']] ?? 'secondary';
                                $statusText = $statusTexts[$apt['status']] ?? $apt['status'];
                                ?>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-<?php echo $statusClass; ?> dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        <?php echo $statusText; ?>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="appointments.php?action=update_status&id=<?php echo $apt['id']; ?>&status=scheduled&date=<?php echo $selectedDate; ?>">مجدول</a></li>
                                        <li><a class="dropdown-item" href="appointments.php?action=update_status&id=<?php echo $apt['id']; ?>&status=confirmed&date=<?php echo $selectedDate; ?>">مؤكد</a></li>
                                        <li><a class="dropdown-item" href="appointments.php?action=update_status&id=<?php echo $apt['id']; ?>&status=completed&date=<?php echo $selectedDate; ?>">مكتمل</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-warning" href="appointments.php?action=update_status&id=<?php echo $apt['id']; ?>&status=cancelled&date=<?php echo $selectedDate; ?>">ملغي</a></li>
                                        <li><a class="dropdown-item text-danger" href="appointments.php?action=update_status&id=<?php echo $apt['id']; ?>&status=no_show&date=<?php echo $selectedDate; ?>">لم يحضر</a></li>
                                    </ul>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="appointments.php?action=edit&id=<?php echo $apt['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if (hasPermission('doctor') && $apt['status'] === 'completed'): ?>
                                    <a href="prescriptions.php?action=add&appointment_id=<?php echo $apt['id']; ?>" 
                                       class="btn btn-sm btn-outline-success" title="وصفة طبية">
                                        <i class="fas fa-prescription-bottle-alt"></i>
                                    </a>
                                    <?php endif; ?>
                                    <a href="billing.php?action=add&appointment_id=<?php echo $apt['id']; ?>" 
                                       class="btn btn-sm btn-outline-info" title="فاتورة">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <?php if (hasPermission('admin')): ?>
                                    <a href="appointments.php?action=delete&id=<?php echo $apt['id']; ?>&date=<?php echo $selectedDate; ?>" 
                                       class="btn btn-sm btn-outline-danger btn-delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تحرير موعد -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-calendar-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
        <?php echo $action === 'add' ? 'حجز موعد جديد' : 'تحرير الموعد'; ?>
    </h2>
    <a href="appointments.php?date=<?php echo $selectedDate; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="patient_id" class="form-label">المريض *</label>
                        <select class="form-control" id="patient_id" name="patient_id" required>
                            <option value="">اختر المريض</option>
                            <?php foreach ($patients as $patient): ?>
                            <option value="<?php echo $patient['id']; ?>" 
                                    <?php echo ($appointment['patient_id'] ?? $_GET['patient_id'] ?? '') == $patient['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($patient['full_name'] . ' - ' . $patient['patient_number']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="doctor_id" class="form-label">الطبيب *</label>
                        <select class="form-control" id="doctor_id" name="doctor_id" required>
                            <option value="">اختر الطبيب</option>
                            <?php foreach ($doctors as $doctor): ?>
                            <option value="<?php echo $doctor['id']; ?>" 
                                    <?php echo ($appointment['doctor_id'] ?? '') == $doctor['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($doctor['full_name'] . ' - ' . $doctor['specialization']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_date" class="form-label">تاريخ الموعد *</label>
                        <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                               value="<?php echo $appointment['appointment_date'] ?? $selectedDate; ?>" required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="appointment_time" class="form-label">وقت الموعد *</label>
                        <input type="time" class="form-control" id="appointment_time" name="appointment_time" 
                               value="<?php echo $appointment['appointment_time'] ?? ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration" class="form-label">مدة الموعد (بالدقائق)</label>
                        <select class="form-control" id="duration" name="duration">
                            <option value="15" <?php echo ($appointment['duration'] ?? 30) == 15 ? 'selected' : ''; ?>>15 دقيقة</option>
                            <option value="30" <?php echo ($appointment['duration'] ?? 30) == 30 ? 'selected' : ''; ?>>30 دقيقة</option>
                            <option value="45" <?php echo ($appointment['duration'] ?? 30) == 45 ? 'selected' : ''; ?>>45 دقيقة</option>
                            <option value="60" <?php echo ($appointment['duration'] ?? 30) == 60 ? 'selected' : ''; ?>>60 دقيقة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الزيارة</label>
                        <input type="text" class="form-control" id="reason" name="reason" 
                               value="<?php echo htmlspecialchars($appointment['reason'] ?? ''); ?>" 
                               placeholder="مثل: كشف دوري، متابعة، استشارة...">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3" 
                          placeholder="أي ملاحظات إضافية..."><?php echo htmlspecialchars($appointment['notes'] ?? ''); ?></textarea>
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    <?php echo $action === 'add' ? 'حجز الموعد' : 'حفظ التغييرات'; ?>
                </button>
                <a href="appointments.php?date=<?php echo $selectedDate; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php endif; ?>

<?php
$inlineJS = "
    // تحديث قائمة المرضى عند البحث
    $('#patient_id').select2({
        placeholder: 'ابحث عن المريض...',
        allowClear: true
    });
    
    // تحديث قائمة الأطباء
    $('#doctor_id').select2({
        placeholder: 'اختر الطبيب...',
        allowClear: true
    });
";

include 'includes/footer.php';
?>
