<?php
/**
 * إعدادات النظام العامة - نظام حكيم لإدارة العيادات
 * General System Configuration - Hakim Clinic Management System
 */

// إعدادات الموقع الأساسية
define('SITE_NAME', 'نظام حكيم لإدارة العيادات');
define('SITE_NAME_EN', 'Hakim Clinic Management System');
define('SITE_URL', 'https://xyz.collectandwin.xyz');
define('SITE_VERSION', '1.0.0');

// إعدادات المسارات
define('ROOT_PATH', dirname(dirname(__FILE__)));
define('UPLOAD_PATH', ROOT_PATH . '/uploads/');
define('ASSETS_PATH', ROOT_PATH . '/assets/');
define('INCLUDES_PATH', ROOT_PATH . '/includes/');

// إعدادات رفع الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// إعدادات الجلسة
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('REMEMBER_ME_DURATION', 30 * 24 * 3600); // 30 يوم

// إعدادات الأمان
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 دقيقة

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات الألوان (حسب المتطلبات)
define('PRIMARY_COLOR', '#007BFF');    // أزرق احترافي
define('SECONDARY_COLOR', '#FFFFFF');  // أبيض
define('SUCCESS_COLOR', '#28A745');    // أخضر طبي
define('DANGER_COLOR', '#DC3545');     // أحمر للتحذيرات
define('WARNING_COLOR', '#FFC107');    // أصفر للتنبيهات
define('INFO_COLOR', '#17A2B8');       // أزرق فاتح للمعلومات

// إعدادات البريد الإلكتروني (للإشعارات)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'نظام حكيم');

// إعدادات SMS (للإشعارات)
define('SMS_PROVIDER', ''); // يمكن إضافة مزود خدمة SMS
define('SMS_API_KEY', '');
define('SMS_SENDER_NAME', 'Hakim');

// أنواع المستخدمين
define('USER_TYPE_ADMIN', 'admin');
define('USER_TYPE_DOCTOR', 'doctor');
define('USER_TYPE_SECRETARY', 'secretary');

// حالات المواعيد
define('APPOINTMENT_STATUS_SCHEDULED', 'scheduled');
define('APPOINTMENT_STATUS_CONFIRMED', 'confirmed');
define('APPOINTMENT_STATUS_COMPLETED', 'completed');
define('APPOINTMENT_STATUS_CANCELLED', 'cancelled');
define('APPOINTMENT_STATUS_NO_SHOW', 'no_show');

// حالات الفواتير
define('BILLING_STATUS_PENDING', 'pending');
define('BILLING_STATUS_PAID', 'paid');
define('BILLING_STATUS_PARTIAL', 'partial');
define('BILLING_STATUS_CANCELLED', 'cancelled');

// إعدادات التشفير
define('ENCRYPTION_KEY', 'hakim_clinic_2024_secure_key');
define('HASH_ALGORITHM', 'sha256');

// إعدادات التطبيق
define('ENABLE_REGISTRATION', false); // تعطيل التسجيل العام
define('ENABLE_EMAIL_VERIFICATION', false);
define('ENABLE_SMS_NOTIFICATIONS', false);
define('ENABLE_EMAIL_NOTIFICATIONS', true);

// إعدادات التصحيح والتطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('LOG_PATH', ROOT_PATH . '/logs/');

// دالة للحصول على URL كامل
function getFullUrl($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
        return '';
    }
    return date($format, strtotime($date));
}

// دالة لتنسيق المبلغ المالي
function formatCurrency($amount) {
    return number_format($amount, 2) . ' ريال';
}

// دالة للحصول على اللون حسب النوع
function getColorByType($type) {
    switch ($type) {
        case 'primary': return PRIMARY_COLOR;
        case 'secondary': return SECONDARY_COLOR;
        case 'success': return SUCCESS_COLOR;
        case 'danger': return DANGER_COLOR;
        case 'warning': return WARNING_COLOR;
        case 'info': return INFO_COLOR;
        default: return PRIMARY_COLOR;
    }
}
?>
