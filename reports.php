<?php
/**
 * التقارير والإحصائيات - نظام حكيم لإدارة العيادات
 * Reports and Statistics - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات
if (!hasPermission('secretary')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    redirect('dashboard.php');
}

$pageTitle = 'التقارير والإحصائيات';

// تحديد الفترة الزمنية
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // اليوم الحالي
$reportType = $_GET['report_type'] ?? 'overview';

// جلب الإحصائيات العامة
try {
    // إحصائيات المرضى
    $totalPatientsQuery = "SELECT COUNT(*) as count FROM patients WHERE DATE(created_at) BETWEEN ? AND ?";
    $newPatients = fetchOne($totalPatientsQuery, [$startDate, $endDate])['count'];
    
    $totalPatientsAllQuery = "SELECT COUNT(*) as count FROM patients";
    $totalPatients = fetchOne($totalPatientsAllQuery)['count'];
    
    // إحصائيات المواعيد
    $appointmentsQuery = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN status = 'no_show' THEN 1 ELSE 0 END) as no_show
        FROM appointments 
        WHERE appointment_date BETWEEN ? AND ?
    ";
    $appointmentsStats = fetchOne($appointmentsQuery, [$startDate, $endDate]);
    
    // الإحصائيات المالية
    $revenueQuery = "
        SELECT 
            COALESCE(SUM(total_amount), 0) as total_revenue,
            COALESCE(SUM(paid_amount), 0) as paid_amount,
            COALESCE(SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END), 0) as completed_revenue,
            COUNT(*) as total_invoices
        FROM billing 
        WHERE invoice_date BETWEEN ? AND ?
    ";
    $revenueStats = fetchOne($revenueQuery, [$startDate, $endDate]);
    
    // أكثر الخدمات طلباً
    $topServicesQuery = "
        SELECT 
            bi.service_name,
            SUM(bi.quantity) as total_quantity,
            SUM(bi.total_price) as total_revenue,
            COUNT(DISTINCT bi.billing_id) as invoice_count
        FROM billing_items bi
        JOIN billing b ON bi.billing_id = b.id
        WHERE b.invoice_date BETWEEN ? AND ?
        GROUP BY bi.service_name
        ORDER BY total_quantity DESC
        LIMIT 10
    ";
    $topServices = fetchAll($topServicesQuery, [$startDate, $endDate]);
    
    // أكثر الأطباء نشاطاً
    $topDoctorsQuery = "
        SELECT 
            u.full_name,
            u.specialization,
            COUNT(a.id) as appointment_count,
            SUM(CASE WHEN a.status = 'completed' THEN 1 ELSE 0 END) as completed_appointments
        FROM users u
        LEFT JOIN appointments a ON u.id = a.doctor_id AND a.appointment_date BETWEEN ? AND ?
        WHERE u.user_type = 'doctor' AND u.is_active = 1
        GROUP BY u.id
        ORDER BY appointment_count DESC
    ";
    $topDoctors = fetchAll($topDoctorsQuery, [$startDate, $endDate]);
    
    // الإيرادات اليومية (للرسم البياني)
    $dailyRevenueQuery = "
        SELECT 
            DATE(invoice_date) as date,
            SUM(total_amount) as revenue,
            COUNT(*) as invoice_count
        FROM billing 
        WHERE invoice_date BETWEEN ? AND ? AND status = 'paid'
        GROUP BY DATE(invoice_date)
        ORDER BY date
    ";
    $dailyRevenue = fetchAll($dailyRevenueQuery, [$startDate, $endDate]);
    
    // المواعيد الشهرية (للرسم البياني)
    $monthlyAppointmentsQuery = "
        SELECT 
            DATE_FORMAT(appointment_date, '%Y-%m') as month,
            COUNT(*) as total,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
        FROM appointments 
        WHERE appointment_date BETWEEN DATE_SUB(?, INTERVAL 11 MONTH) AND ?
        GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
        ORDER BY month
    ";
    $monthlyAppointments = fetchAll($monthlyAppointmentsQuery, [$endDate, $endDate]);
    
} catch (Exception $e) {
    error_log("Reports error: " . $e->getMessage());
    $newPatients = $totalPatients = 0;
    $appointmentsStats = ['total' => 0, 'completed' => 0, 'cancelled' => 0, 'no_show' => 0];
    $revenueStats = ['total_revenue' => 0, 'paid_amount' => 0, 'completed_revenue' => 0, 'total_invoices' => 0];
    $topServices = $topDoctors = $dailyRevenue = $monthlyAppointments = [];
}

include 'includes/header.php';
?>

<!-- فلتر التواريخ -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
            </div>
            <div class="col-md-3">
                <label for="report_type" class="form-label">نوع التقرير</label>
                <select class="form-control" id="report_type" name="report_type">
                    <option value="overview" <?php echo $reportType === 'overview' ? 'selected' : ''; ?>>نظرة عامة</option>
                    <option value="financial" <?php echo $reportType === 'financial' ? 'selected' : ''; ?>>مالي</option>
                    <option value="appointments" <?php echo $reportType === 'appointments' ? 'selected' : ''; ?>>المواعيد</option>
                    <option value="patients" <?php echo $reportType === 'patients' ? 'selected' : ''; ?>>المرضى</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>عرض التقرير
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i>تصدير
                </button>
            </div>
        </form>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="text-primary"><?php echo number_format($newPatients); ?></h4>
                <p class="mb-0">مرضى جدد</p>
                <small class="text-muted">من <?php echo number_format($totalPatients); ?> إجمالي</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                <h4 class="text-info"><?php echo number_format($appointmentsStats['total']); ?></h4>
                <p class="mb-0">إجمالي المواعيد</p>
                <small class="text-muted"><?php echo number_format($appointmentsStats['completed']); ?> مكتمل</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                <h4 class="text-success"><?php echo formatCurrency($revenueStats['completed_revenue']); ?></h4>
                <p class="mb-0">الإيرادات المحققة</p>
                <small class="text-muted">من <?php echo formatCurrency($revenueStats['total_revenue']); ?> إجمالي</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-file-invoice fa-2x text-warning mb-2"></i>
                <h4 class="text-warning"><?php echo number_format($revenueStats['total_invoices']); ?></h4>
                <p class="mb-0">عدد الفواتير</p>
                <small class="text-muted">
                    <?php 
                    $avgInvoice = $revenueStats['total_invoices'] > 0 ? $revenueStats['total_revenue'] / $revenueStats['total_invoices'] : 0;
                    echo formatCurrency($avgInvoice) . ' متوسط';
                    ?>
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرسم البياني للإيرادات اليومية -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإيرادات اليومية</h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- توزيع حالات المواعيد -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>حالات المواعيد</h5>
            </div>
            <div class="card-body">
                <canvas id="appointmentsChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أكثر الخدمات طلباً -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-star me-2"></i>أكثر الخدمات طلباً</h5>
            </div>
            <div class="card-body">
                <?php if (empty($topServices)): ?>
                    <p class="text-muted text-center">لا توجد بيانات للفترة المحددة</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الخدمة</th>
                                    <th>العدد</th>
                                    <th>الإيراد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topServices as $service): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($service['service_name']); ?></td>
                                    <td><?php echo number_format($service['total_quantity']); ?></td>
                                    <td><?php echo formatCurrency($service['total_revenue']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- أداء الأطباء -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-md me-2"></i>أداء الأطباء</h5>
            </div>
            <div class="card-body">
                <?php if (empty($topDoctors)): ?>
                    <p class="text-muted text-center">لا توجد بيانات للفترة المحددة</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الطبيب</th>
                                    <th>المواعيد</th>
                                    <th>المكتملة</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topDoctors as $doctor): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($doctor['full_name']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($doctor['specialization']); ?></small>
                                    </td>
                                    <td><?php echo number_format($doctor['appointment_count']); ?></td>
                                    <td><?php echo number_format($doctor['completed_appointments']); ?></td>
                                    <td>
                                        <?php 
                                        $percentage = $doctor['appointment_count'] > 0 ? 
                                            ($doctor['completed_appointments'] / $doctor['appointment_count']) * 100 : 0;
                                        echo number_format($percentage, 1) . '%';
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني للمواعيد الشهرية -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>المواعيد الشهرية (آخر 12 شهر)</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<?php
$inlineJS = "
// بيانات الرسوم البيانية
const dailyRevenueData = " . json_encode($dailyRevenue) . ";
const appointmentsData = " . json_encode($appointmentsStats) . ";
const monthlyData = " . json_encode($monthlyAppointments) . ";

// رسم بياني للإيرادات اليومية
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: dailyRevenueData.map(item => item.date),
        datasets: [{
            label: 'الإيرادات اليومية',
            data: dailyRevenueData.map(item => item.revenue),
            borderColor: '#007BFF',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// رسم دائري لحالات المواعيد
const appointmentsCtx = document.getElementById('appointmentsChart').getContext('2d');
new Chart(appointmentsCtx, {
    type: 'doughnut',
    data: {
        labels: ['مكتملة', 'ملغية', 'لم يحضر', 'أخرى'],
        datasets: [{
            data: [
                appointmentsData.completed,
                appointmentsData.cancelled,
                appointmentsData.no_show,
                appointmentsData.total - appointmentsData.completed - appointmentsData.cancelled - appointmentsData.no_show
            ],
            backgroundColor: ['#28A745', '#DC3545', '#6C757D', '#FFC107']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للمواعيد الشهرية
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'إجمالي المواعيد',
            data: monthlyData.map(item => item.total),
            backgroundColor: '#007BFF'
        }, {
            label: 'المواعيد المكتملة',
            data: monthlyData.map(item => item.completed),
            backgroundColor: '#28A745'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// دالة تصدير التقرير
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open('export_report.php?' + params.toString(), '_blank');
}
";

include 'includes/footer.php';
?>
