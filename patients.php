<?php
/**
 * إدارة المرضى - نظام حكيم لإدارة العيادات
 * Patients Management - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات
if (!hasPermission('secretary')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    redirect('dashboard.php');
}

$pageTitle = 'إدارة المرضى';
$action = $_GET['action'] ?? 'list';
$patientId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $data = [
            'first_name' => sanitizeInput($_POST['first_name']),
            'last_name' => sanitizeInput($_POST['last_name']),
            'date_of_birth' => convertArabicDateToMysql($_POST['date_of_birth']),
            'gender' => sanitizeInput($_POST['gender']),
            'phone' => sanitizeInput($_POST['phone']),
            'email' => sanitizeInput($_POST['email']),
            'address' => sanitizeInput($_POST['address']),
            'national_id' => sanitizeInput($_POST['national_id']),
            'emergency_contact_name' => sanitizeInput($_POST['emergency_contact_name']),
            'emergency_contact_phone' => sanitizeInput($_POST['emergency_contact_phone']),
            'blood_type' => sanitizeInput($_POST['blood_type']),
            'allergies' => sanitizeInput($_POST['allergies']),
            'chronic_diseases' => sanitizeInput($_POST['chronic_diseases']),
            'notes' => sanitizeInput($_POST['notes'])
        ];
        
        // التحقق من صحة البيانات
        $errors = [];
        if (empty($data['first_name'])) $errors[] = 'الاسم الأول مطلوب';
        if (empty($data['last_name'])) $errors[] = 'اسم العائلة مطلوب';
        if (empty($data['phone'])) $errors[] = 'رقم الهاتف مطلوب';
        if (!empty($data['phone']) && !isValidSaudiPhone($data['phone'])) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        if (!empty($data['email']) && !isValidEmail($data['email'])) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        if (!empty($data['national_id']) && !isValidSaudiId($data['national_id'])) {
            $errors[] = 'رقم الهوية الوطنية غير صحيح';
        }
        
        if (empty($errors)) {
            try {
                if ($action === 'add') {
                    // إضافة مريض جديد
                    $data['patient_number'] = generatePatientNumber();
                    $data['created_by'] = $_SESSION['user_id'];
                    
                    $query = "INSERT INTO patients (patient_number, first_name, last_name, date_of_birth, gender, phone, email, address, national_id, emergency_contact_name, emergency_contact_phone, blood_type, allergies, chronic_diseases, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $params = [
                        $data['patient_number'], $data['first_name'], $data['last_name'],
                        $data['date_of_birth'], $data['gender'], $data['phone'],
                        $data['email'], $data['address'], $data['national_id'],
                        $data['emergency_contact_name'], $data['emergency_contact_phone'],
                        $data['blood_type'], $data['allergies'], $data['chronic_diseases'],
                        $data['notes'], $data['created_by']
                    ];
                    
                    executeQuery($query, $params);
                    $newPatientId = getLastInsertId();
                    
                    logActivity($_SESSION['user_id'], 'إضافة مريض جديد', 'patients', $newPatientId, null, $data);
                    $_SESSION['success_message'] = 'تم إضافة المريض بنجاح';
                    
                } else {
                    // تحديث مريض موجود
                    $query = "UPDATE patients SET first_name=?, last_name=?, date_of_birth=?, gender=?, phone=?, email=?, address=?, national_id=?, emergency_contact_name=?, emergency_contact_phone=?, blood_type=?, allergies=?, chronic_diseases=?, notes=? WHERE id=?";
                    
                    $params = [
                        $data['first_name'], $data['last_name'], $data['date_of_birth'],
                        $data['gender'], $data['phone'], $data['email'], $data['address'],
                        $data['national_id'], $data['emergency_contact_name'],
                        $data['emergency_contact_phone'], $data['blood_type'],
                        $data['allergies'], $data['chronic_diseases'], $data['notes'], $patientId
                    ];
                    
                    executeQuery($query, $params);
                    
                    logActivity($_SESSION['user_id'], 'تحديث بيانات مريض', 'patients', $patientId, null, $data);
                    $_SESSION['success_message'] = 'تم تحديث بيانات المريض بنجاح';
                }
                
                redirect('patients.php');
                
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    }
}

// حذف مريض
if ($action === 'delete' && $patientId) {
    try {
        // التحقق من وجود مواعيد للمريض
        $appointmentsQuery = "SELECT COUNT(*) as count FROM appointments WHERE patient_id = ?";
        $appointmentsCount = fetchOne($appointmentsQuery, [$patientId])['count'];
        
        if ($appointmentsCount > 0) {
            $_SESSION['error_message'] = 'لا يمكن حذف المريض لوجود مواعيد مرتبطة به';
        } else {
            $query = "DELETE FROM patients WHERE id = ?";
            executeQuery($query, [$patientId]);
            
            logActivity($_SESSION['user_id'], 'حذف مريض', 'patients', $patientId);
            $_SESSION['success_message'] = 'تم حذف المريض بنجاح';
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ في الحذف: ' . $e->getMessage();
    }
    
    redirect('patients.php');
}

// جلب بيانات المريض للتحرير
$patient = null;
if ($action === 'edit' && $patientId) {
    $query = "SELECT * FROM patients WHERE id = ?";
    $patient = fetchOne($query, [$patientId]);
    
    if (!$patient) {
        $_SESSION['error_message'] = 'المريض غير موجود';
        redirect('patients.php');
    }
}

// جلب قائمة المرضى
$searchTerm = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$whereClause = '';
$params = [];

if (!empty($searchTerm)) {
    $whereClause = "WHERE CONCAT(first_name, ' ', last_name) LIKE ? OR phone LIKE ? OR patient_number LIKE ?";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam];
}

$countQuery = "SELECT COUNT(*) as total FROM patients $whereClause";
$totalPatients = fetchOne($countQuery, $params)['total'];
$totalPages = ceil($totalPatients / $limit);

$patientsQuery = "SELECT * FROM patients $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$patients = fetchAll($patientsQuery, $params);

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- قائمة المرضى -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>قائمة المرضى</h2>
    <a href="patients.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>إضافة مريض جديد
    </a>
</div>

<!-- شريط البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" name="search" 
                       placeholder="البحث بالاسم أو رقم الهاتف أو رقم المريض..." 
                       value="<?php echo htmlspecialchars($searchTerm); ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="patients.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول المرضى -->
<div class="card">
    <div class="card-body">
        <?php if (empty($patients)): ?>
            <div class="text-center py-5">
                <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مرضى</h5>
                <p class="text-muted">ابدأ بإضافة مريض جديد</p>
                <a href="patients.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مريض جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم المريض</th>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>العمر</th>
                            <th>الجنس</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $p): ?>
                        <tr class="searchable-row">
                            <td><strong><?php echo htmlspecialchars($p['patient_number']); ?></strong></td>
                            <td><?php echo htmlspecialchars($p['first_name'] . ' ' . $p['last_name']); ?></td>
                            <td>
                                <a href="tel:<?php echo $p['phone']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($p['phone']); ?>
                                </a>
                            </td>
                            <td>
                                <?php 
                                $age = calculateAge($p['date_of_birth']);
                                echo $age ? $age . ' سنة' : '-';
                                ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $p['gender'] === 'male' ? 'primary' : 'pink'; ?>">
                                    <?php echo $p['gender'] === 'male' ? 'ذكر' : 'أنثى'; ?>
                                </span>
                            </td>
                            <td><?php echo formatDateForDisplay($p['created_at']); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="patients.php?action=view&id=<?php echo $p['id']; ?>" 
                                       class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="patients.php?action=edit&id=<?php echo $p['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="appointments.php?action=add&patient_id=<?php echo $p['id']; ?>" 
                                       class="btn btn-sm btn-outline-success" title="حجز موعد">
                                        <i class="fas fa-calendar-plus"></i>
                                    </a>
                                    <?php if (hasPermission('admin')): ?>
                                    <a href="patients.php?action=delete&id=<?php echo $p['id']; ?>" 
                                       class="btn btn-sm btn-outline-danger btn-delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تحرير مريض -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-user-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
        <?php echo $action === 'add' ? 'إضافة مريض جديد' : 'تحرير بيانات المريض'; ?>
    </h2>
    <a href="patients.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
            <div class="row">
                <!-- البيانات الأساسية -->
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-user me-2"></i>البيانات الأساسية</h5>
                    
                    <div class="mb-3">
                        <label for="first_name" class="form-label">الاسم الأول *</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" 
                               value="<?php echo htmlspecialchars($patient['first_name'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="last_name" class="form-label">اسم العائلة *</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" 
                               value="<?php echo htmlspecialchars($patient['last_name'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                        <input type="text" class="form-control date-picker" id="date_of_birth" name="date_of_birth" 
                               value="<?php echo $patient ? formatDateForDisplay($patient['date_of_birth']) : ''; ?>" 
                               placeholder="dd/mm/yyyy">
                    </div>
                    
                    <div class="mb-3">
                        <label for="gender" class="form-label">الجنس *</label>
                        <select class="form-control" id="gender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="male" <?php echo ($patient['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>ذكر</option>
                            <option value="female" <?php echo ($patient['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>أنثى</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="blood_type" class="form-label">فصيلة الدم</label>
                        <select class="form-control" id="blood_type" name="blood_type">
                            <option value="">اختر فصيلة الدم</option>
                            <?php 
                            $bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
                            foreach ($bloodTypes as $type): 
                            ?>
                            <option value="<?php echo $type; ?>" <?php echo ($patient['blood_type'] ?? '') === $type ? 'selected' : ''; ?>>
                                <?php echo $type; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <!-- معلومات الاتصال -->
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-phone me-2"></i>معلومات الاتصال</h5>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف *</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($patient['phone'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($patient['email'] ?? ''); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($patient['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                        <input type="text" class="form-control" id="national_id" name="national_id" 
                               value="<?php echo htmlspecialchars($patient['national_id'] ?? ''); ?>">
                    </div>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <!-- جهة الاتصال في الطوارئ -->
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-exclamation-triangle me-2"></i>جهة الاتصال في الطوارئ</h5>
                    
                    <div class="mb-3">
                        <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال</label>
                        <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                               value="<?php echo htmlspecialchars($patient['emergency_contact_name'] ?? ''); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="emergency_contact_phone" class="form-label">رقم هاتف جهة الاتصال</label>
                        <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                               value="<?php echo htmlspecialchars($patient['emergency_contact_phone'] ?? ''); ?>">
                    </div>
                </div>
                
                <!-- المعلومات الطبية -->
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-heartbeat me-2"></i>المعلومات الطبية</h5>
                    
                    <div class="mb-3">
                        <label for="allergies" class="form-label">الحساسية</label>
                        <textarea class="form-control" id="allergies" name="allergies" rows="2" 
                                  placeholder="اذكر أي حساسية معروفة..."><?php echo htmlspecialchars($patient['allergies'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="chronic_diseases" class="form-label">الأمراض المزمنة</label>
                        <textarea class="form-control" id="chronic_diseases" name="chronic_diseases" rows="2" 
                                  placeholder="اذكر أي أمراض مزمنة..."><?php echo htmlspecialchars($patient['chronic_diseases'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات إضافية</label>
                <textarea class="form-control" id="notes" name="notes" rows="3" 
                          placeholder="أي ملاحظات إضافية..."><?php echo htmlspecialchars($patient['notes'] ?? ''); ?></textarea>
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    <?php echo $action === 'add' ? 'إضافة المريض' : 'حفظ التغييرات'; ?>
                </button>
                <a href="patients.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php endif; ?>

<?php
$additionalJS = ['https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css'];
include 'includes/footer.php';
?>
