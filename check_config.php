<?php
/**
 * فحص إعدادات النظام - نظام حكيم لإدارة العيادات
 * System Configuration Check - Hakim Clinic Management System
 */

// عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>فحص إعدادات نظام حكيم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Cairo', sans-serif; }</style>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h3><i class='fas fa-stethoscope'></i> فحص إعدادات نظام حكيم</h3>";
echo "</div>";
echo "<div class='card-body'>";

// فحص ملفات الإعدادات
echo "<h5 class='text-primary'>1. فحص ملفات الإعدادات</h5>";

if (file_exists('config/config.php')) {
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> ملف config.php موجود</div>";
    require_once 'config/config.php';
    
    echo "<table class='table table-sm'>";
    echo "<tr><td><strong>اسم الموقع:</strong></td><td>" . SITE_NAME . "</td></tr>";
    echo "<tr><td><strong>رابط الموقع:</strong></td><td>" . SITE_URL . "</td></tr>";
    echo "<tr><td><strong>إصدار النظام:</strong></td><td>" . SITE_VERSION . "</td></tr>";
    echo "</table>";
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> ملف config.php غير موجود</div>";
}

if (file_exists('config/database.php')) {
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> ملف database.php موجود</div>";
    require_once 'config/database.php';
    
    echo "<table class='table table-sm'>";
    echo "<tr><td><strong>خادم قاعدة البيانات:</strong></td><td>" . DB_HOST . "</td></tr>";
    echo "<tr><td><strong>اسم قاعدة البيانات:</strong></td><td>" . DB_NAME . "</td></tr>";
    echo "<tr><td><strong>مستخدم قاعدة البيانات:</strong></td><td>" . DB_USER . "</td></tr>";
    echo "<tr><td><strong>كلمة المرور:</strong></td><td>" . (DB_PASS ? '***مخفية***' : 'فارغة') . "</td></tr>";
    echo "</table>";
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> ملف database.php غير موجود</div>";
}

// فحص الاتصال بقاعدة البيانات
echo "<h5 class='text-primary mt-4'>2. فحص الاتصال بقاعدة البيانات</h5>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // فحص الجداول
    $tables = ['users', 'patients', 'appointments', 'prescriptions', 'billing'];
    $existingTables = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            $existingTables[] = $table;
        }
    }
    
    if (count($existingTables) === count($tables)) {
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> جميع الجداول المطلوبة موجودة (" . count($existingTables) . "/" . count($tables) . ")</div>";
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> بعض الجداول مفقودة (" . count($existingTables) . "/" . count($tables) . ")</div>";
        echo "<p>الجداول الموجودة: " . implode(', ', $existingTables) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}

// فحص متطلبات PHP
echo "<h5 class='text-primary mt-4'>3. فحص متطلبات PHP</h5>";

$requirements = [
    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'mbstring Extension' => extension_loaded('mbstring'),
    'OpenSSL Extension' => extension_loaded('openssl'),
    'JSON Extension' => extension_loaded('json'),
    'FileInfo Extension' => extension_loaded('fileinfo')
];

foreach ($requirements as $requirement => $status) {
    if ($status) {
        echo "<div class='alert alert-success py-2'><i class='fas fa-check'></i> $requirement</div>";
    } else {
        echo "<div class='alert alert-danger py-2'><i class='fas fa-times'></i> $requirement</div>";
    }
}

// فحص الملفات والمجلدات
echo "<h5 class='text-primary mt-4'>4. فحص الملفات والمجلدات</h5>";

$files = [
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'dashboard.php' => 'لوحة التحكم',
    'patients.php' => 'إدارة المرضى',
    'appointments.php' => 'إدارة المواعيد',
    'prescriptions.php' => 'الوصفات الطبية',
    'billing.php' => 'الفواتير',
    'reports.php' => 'التقارير',
    'users.php' => 'إدارة المستخدمين',
    'settings.php' => 'الإعدادات'
];

$missingFiles = [];
foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='alert alert-success py-1'><i class='fas fa-check'></i> $file - $description</div>";
    } else {
        echo "<div class='alert alert-danger py-1'><i class='fas fa-times'></i> $file - $description</div>";
        $missingFiles[] = $file;
    }
}

$directories = [
    'config' => 'مجلد الإعدادات',
    'includes' => 'الملفات المشتركة',
    'assets' => 'الملفات الثابتة',
    'uploads' => 'ملفات الرفع',
    'database' => 'ملفات قاعدة البيانات'
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? ' (قابل للكتابة)' : ' (غير قابل للكتابة)';
        echo "<div class='alert alert-success py-1'><i class='fas fa-folder'></i> $dir/ - $description$writable</div>";
    } else {
        echo "<div class='alert alert-danger py-1'><i class='fas fa-times'></i> $dir/ - $description</div>";
    }
}

// الخلاصة والتوصيات
echo "<h5 class='text-primary mt-4'>5. الخلاصة والتوصيات</h5>";

$allRequirementsMet = true;
foreach ($requirements as $status) {
    if (!$status) {
        $allRequirementsMet = false;
        break;
    }
}

if ($allRequirementsMet && empty($missingFiles)) {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-thumbs-up'></i> النظام جاهز للتثبيت!</h6>";
    echo "<p>جميع المتطلبات متوفرة والملفات موجودة.</p>";
    echo "<a href='install.php' class='btn btn-primary'><i class='fas fa-play'></i> بدء التثبيت</a>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h6><i class='fas fa-exclamation-triangle'></i> يرجى حل المشاكل التالية:</h6>";
    echo "<ul>";
    
    foreach ($requirements as $requirement => $status) {
        if (!$status) {
            echo "<li>تفعيل: $requirement</li>";
        }
    }
    
    foreach ($missingFiles as $file) {
        echo "<li>رفع الملف: $file</li>";
    }
    
    echo "</ul>";
    echo "</div>";
}

// معلومات إضافية
echo "<h5 class='text-primary mt-4'>6. معلومات النظام</h5>";
echo "<table class='table table-sm'>";
echo "<tr><td><strong>إصدار PHP:</strong></td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td><strong>نظام التشغيل:</strong></td><td>" . PHP_OS . "</td></tr>";
echo "<tr><td><strong>خادم الويب:</strong></td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</td></tr>";
echo "<tr><td><strong>الذاكرة المتاحة:</strong></td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td><strong>حد رفع الملفات:</strong></td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td><strong>وقت التنفيذ الأقصى:</strong></td><td>" . ini_get('max_execution_time') . "s</td></tr>";
echo "</table>";

echo "</div>"; // card-body
echo "</div>"; // card

echo "<div class='text-center mt-3'>";
echo "<small class='text-muted'>نظام حكيم لإدارة العيادات - فحص الإعدادات</small>";
echo "</div>";

echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "</body>";
echo "</html>";
?>
