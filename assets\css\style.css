/* نظام حكيم لإدارة العيادات - ملف الأنماط الرئيسي */
/* Hakim Clinic Management System - Main Stylesheet */

/* استيراد خط Cairo من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* المتغيرات الأساسية للألوان */
:root {
  --primary-color: #007BFF;      /* أزرق احترافي */
  --secondary-color: #FFFFFF;    /* أبيض */
  --success-color: #28A745;      /* أخضر طبي */
  --danger-color: #DC3545;       /* أحمر */
  --warning-color: #FFC107;      /* أصفر */
  --info-color: #17A2B8;         /* أزرق فاتح */
  --light-color: #F8F9FA;        /* رمادي فاتح */
  --dark-color: #343A40;         /* رمادي داكن */
  
  /* ألوان إضافية للتدرجات */
  --primary-light: #66B2FF;
  --primary-dark: #0056B3;
  --success-light: #71DD8A;
  --success-dark: #1E7E34;
  
  /* خصائص الخط */
  --font-family-ar: 'Cairo', sans-serif;
  --font-family-en: 'Roboto', sans-serif;
  
  /* المسافات والأحجام */
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* إعدادات عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-ar);
  direction: rtl;
  text-align: right;
  background-color: var(--light-color);
  color: var(--dark-color);
  line-height: 1.6;
}

/* إعدادات للنصوص الإنجليزية */
.en-text {
  font-family: var(--font-family-en);
  direction: ltr;
  text-align: left;
}

/* الحاوي الرئيسي */
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* شريط التنقل العلوي */
.navbar {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  box-shadow: var(--box-shadow);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--secondary-color) !important;
}

.navbar-nav .nav-link {
  color: var(--secondary-color) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
  background-color: var(--success-color);
  color: var(--secondary-color) !important;
}

/* الشريط الجانبي */
.sidebar {
  background: var(--secondary-color);
  box-shadow: var(--box-shadow);
  min-height: calc(100vh - 76px);
  padding: 2rem 0;
}

.sidebar .nav-link {
  color: var(--dark-color);
  padding: 1rem 1.5rem;
  border-radius: 0;
  transition: var(--transition);
  border-right: 3px solid transparent;
}

.sidebar .nav-link:hover {
  background-color: var(--light-color);
  border-right-color: var(--primary-color);
  transform: translateX(-5px);
}

.sidebar .nav-link.active {
  background-color: var(--primary-color);
  color: var(--secondary-color);
  border-right-color: var(--success-color);
}

.sidebar .nav-link i {
  margin-left: 0.5rem;
  width: 20px;
  text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
  flex: 1;
  padding: 2rem;
  background-color: var(--light-color);
}

/* البطاقات */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  background-color: var(--secondary-color);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--secondary-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
  padding: 1rem 1.5rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

/* الأزرار */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--secondary-color);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), var(--success-dark));
  color: var(--secondary-color);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--success-dark), var(--success-color));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: var(--secondary-color);
  transform: translateY(-2px);
}

/* النماذج */
.form-control {
  border: 2px solid #E9ECEF;
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  transition: var(--transition);
  font-family: var(--font-family-ar);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

/* الجداول */
.table {
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--secondary-color);
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: var(--light-color);
}

.table tbody td {
  padding: 1rem;
  border-color: #E9ECEF;
}

/* الشارات */
.badge {
  font-size: 0.8rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
}

/* التنبيهات */
.alert {
  border: none;
  border-radius: var(--border-radius);
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-dark);
  border-right: 4px solid var(--success-color);
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border-right: 4px solid var(--danger-color);
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #856404;
  border-right: 4px solid var(--warning-color);
}

/* بطاقات الإحصائيات */
.stats-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--secondary-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  transition: var(--transition);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
}

.stats-card.success {
  background: linear-gradient(135deg, var(--success-color), var(--success-light));
}

.stats-card.success:hover {
  box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.stats-card .stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-card .stats-label {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 76px;
    right: -250px;
    width: 250px;
    height: calc(100vh - 76px);
    z-index: 1000;
    transition: var(--transition);
  }
  
  .sidebar.show {
    right: 0;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .stats-card .stats-number {
    font-size: 2rem;
  }
}
