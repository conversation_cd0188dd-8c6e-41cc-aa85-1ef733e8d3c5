<?php
/**
 * الفواتير والمحاسبة - نظام حكيم لإدارة العيادات
 * Billing and Accounting - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات
if (!hasPermission('secretary')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    redirect('dashboard.php');
}

$pageTitle = 'الفواتير والمحاسبة';
$action = $_GET['action'] ?? 'list';
$billingId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $data = [
            'patient_id' => intval($_POST['patient_id']),
            'visit_id' => !empty($_POST['visit_id']) ? intval($_POST['visit_id']) : null,
            'appointment_id' => !empty($_POST['appointment_id']) ? intval($_POST['appointment_id']) : null,
            'invoice_date' => $_POST['invoice_date'],
            'discount_amount' => floatval($_POST['discount_amount'] ?? 0),
            'tax_amount' => floatval($_POST['tax_amount'] ?? 0),
            'payment_method' => sanitizeInput($_POST['payment_method'] ?? ''),
            'notes' => sanitizeInput($_POST['notes'])
        ];

        $services = $_POST['services'] ?? [];

        // التحقق من صحة البيانات
        $errors = [];
        if (empty($data['patient_id'])) $errors[] = 'يجب اختيار المريض';
        if (empty($data['invoice_date'])) $errors[] = 'تاريخ الفاتورة مطلوب';
        if (empty($services)) $errors[] = 'يجب إضافة خدمة واحدة على الأقل';

        // حساب المبالغ
        $subtotal = 0;
        foreach ($services as $service) {
            if (!empty($service['service_name']) && !empty($service['unit_price']) && !empty($service['quantity'])) {
                $subtotal += floatval($service['unit_price']) * intval($service['quantity']);
            }
        }

        $data['subtotal'] = $subtotal;
        $data['total_amount'] = $subtotal + $data['tax_amount'] - $data['discount_amount'];
        $data['paid_amount'] = floatval($_POST['paid_amount'] ?? 0);

        // تحديد حالة الفاتورة
        if ($data['paid_amount'] >= $data['total_amount']) {
            $data['status'] = 'paid';
            $data['payment_date'] = date('Y-m-d H:i:s');
        } elseif ($data['paid_amount'] > 0) {
            $data['status'] = 'partial';
            $data['payment_date'] = date('Y-m-d H:i:s');
        } else {
            $data['status'] = 'pending';
            $data['payment_date'] = null;
        }

        if (empty($errors)) {
            try {
                if ($action === 'add') {
                    // إضافة فاتورة جديدة
                    $data['invoice_number'] = generateInvoiceNumber();
                    $data['created_by'] = $_SESSION['user_id'];

                    $query = "INSERT INTO billing (patient_id, visit_id, appointment_id, invoice_number, invoice_date, subtotal, tax_amount, discount_amount, total_amount, paid_amount, status, payment_method, payment_date, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    $params = [
                        $data['patient_id'], $data['visit_id'], $data['appointment_id'],
                        $data['invoice_number'], $data['invoice_date'], $data['subtotal'],
                        $data['tax_amount'], $data['discount_amount'], $data['total_amount'],
                        $data['paid_amount'], $data['status'], $data['payment_method'],
                        $data['payment_date'], $data['notes'], $data['created_by']
                    ];

                    executeQuery($query, $params);
                    $newBillingId = getLastInsertId();

                    // إضافة تفاصيل الفاتورة
                    foreach ($services as $service) {
                        if (!empty($service['service_name']) && !empty($service['unit_price']) && !empty($service['quantity'])) {
                            $serviceQuery = "INSERT INTO billing_items (billing_id, service_id, service_name, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?)";
                            $serviceParams = [
                                $newBillingId,
                                !empty($service['service_id']) ? $service['service_id'] : null,
                                $service['service_name'],
                                $service['quantity'],
                                $service['unit_price'],
                                floatval($service['unit_price']) * intval($service['quantity'])
                            ];
                            executeQuery($serviceQuery, $serviceParams);
                        }
                    }

                    logActivity($_SESSION['user_id'], 'إضافة فاتورة جديدة', 'billing', $newBillingId, null, $data);
                    $_SESSION['success_message'] = 'تم إنشاء الفاتورة بنجاح';

                } else {
                    // تحديث فاتورة موجودة
                    $query = "UPDATE billing SET patient_id=?, visit_id=?, appointment_id=?, invoice_date=?, subtotal=?, tax_amount=?, discount_amount=?, total_amount=?, paid_amount=?, status=?, payment_method=?, payment_date=?, notes=? WHERE id=?";

                    $params = [
                        $data['patient_id'], $data['visit_id'], $data['appointment_id'],
                        $data['invoice_date'], $data['subtotal'], $data['tax_amount'],
                        $data['discount_amount'], $data['total_amount'], $data['paid_amount'],
                        $data['status'], $data['payment_method'], $data['payment_date'],
                        $data['notes'], $billingId
                    ];

                    executeQuery($query, $params);

                    // حذف التفاصيل القديمة وإضافة الجديدة
                    executeQuery("DELETE FROM billing_items WHERE billing_id = ?", [$billingId]);

                    foreach ($services as $service) {
                        if (!empty($service['service_name']) && !empty($service['unit_price']) && !empty($service['quantity'])) {
                            $serviceQuery = "INSERT INTO billing_items (billing_id, service_id, service_name, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?)";
                            $serviceParams = [
                                $billingId,
                                !empty($service['service_id']) ? $service['service_id'] : null,
                                $service['service_name'],
                                $service['quantity'],
                                $service['unit_price'],
                                floatval($service['unit_price']) * intval($service['quantity'])
                            ];
                            executeQuery($serviceQuery, $serviceParams);
                        }
                    }

                    logActivity($_SESSION['user_id'], 'تحديث فاتورة', 'billing', $billingId, null, $data);
                    $_SESSION['success_message'] = 'تم تحديث الفاتورة بنجاح';
                }

                redirect('billing.php');

            } catch (Exception $e) {
                $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    }
}

// تحديث حالة الدفع
if ($action === 'update_payment' && $billingId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $paidAmount = floatval($_POST['paid_amount']);
    $paymentMethod = sanitizeInput($_POST['payment_method']);

    try {
        // جلب بيانات الفاتورة
        $billQuery = "SELECT * FROM billing WHERE id = ?";
        $bill = fetchOne($billQuery, [$billingId]);

        if ($bill) {
            $newPaidAmount = $bill['paid_amount'] + $paidAmount;

            // تحديد الحالة الجديدة
            if ($newPaidAmount >= $bill['total_amount']) {
                $newStatus = 'paid';
            } elseif ($newPaidAmount > 0) {
                $newStatus = 'partial';
            } else {
                $newStatus = 'pending';
            }

            $updateQuery = "UPDATE billing SET paid_amount = ?, status = ?, payment_method = ?, payment_date = NOW() WHERE id = ?";
            executeQuery($updateQuery, [$newPaidAmount, $newStatus, $paymentMethod, $billingId]);

            logActivity($_SESSION['user_id'], 'تحديث دفعة', 'billing', $billingId, null, ['paid_amount' => $paidAmount, 'payment_method' => $paymentMethod]);
            $_SESSION['success_message'] = 'تم تحديث الدفعة بنجاح';
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ في التحديث: ' . $e->getMessage();
    }

    redirect('billing.php');
}

// حذف فاتورة
if ($action === 'delete' && $billingId) {
    try {
        if (hasPermission('admin')) {
            $query = "DELETE FROM billing WHERE id = ?";
            executeQuery($query, [$billingId]);

            logActivity($_SESSION['user_id'], 'حذف فاتورة', 'billing', $billingId);
            $_SESSION['success_message'] = 'تم حذف الفاتورة بنجاح';
        } else {
            $_SESSION['error_message'] = 'ليس لديك صلاحية لحذف الفواتير';
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ في الحذف: ' . $e->getMessage();
    }

    redirect('billing.php');
}

// جلب بيانات الفاتورة للتحرير
$billing = null;
$billingItems = [];
if ($action === 'edit' && $billingId) {
    $query = "SELECT * FROM billing WHERE id = ?";
    $billing = fetchOne($query, [$billingId]);

    if (!$billing) {
        $_SESSION['error_message'] = 'الفاتورة غير موجودة';
        redirect('billing.php');
    }

    // جلب تفاصيل الفاتورة
    $itemsQuery = "SELECT * FROM billing_items WHERE billing_id = ?";
    $billingItems = fetchAll($itemsQuery, [$billingId]);
}

// جلب قائمة المرضى
$patientsQuery = "SELECT id, CONCAT(first_name, ' ', last_name) as full_name, phone, patient_number FROM patients ORDER BY first_name";
$patients = fetchAll($patientsQuery);

// جلب قائمة الخدمات
$servicesQuery = "SELECT * FROM services WHERE is_active = 1 ORDER BY name";
$services = fetchAll($servicesQuery);

// جلب قائمة الفواتير
$searchTerm = $_GET['search'] ?? '';
$statusFilter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$whereClause = "WHERE 1=1";
$params = [];

if (!empty($searchTerm)) {
    $whereClause .= " AND (CONCAT(p.first_name, ' ', p.last_name) LIKE ? OR p.patient_number LIKE ? OR b.invoice_number LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if (!empty($statusFilter)) {
    $whereClause .= " AND b.status = ?";
    $params[] = $statusFilter;
}

$countQuery = "SELECT COUNT(*) as total FROM billing b JOIN patients p ON b.patient_id = p.id $whereClause";
$totalBilling = fetchOne($countQuery, $params)['total'];
$totalPages = ceil($totalBilling / $limit);

$billingQuery = "
    SELECT b.*,
           CONCAT(p.first_name, ' ', p.last_name) as patient_name,
           p.patient_number,
           u.full_name as created_by_name
    FROM billing b
    JOIN patients p ON b.patient_id = p.id
    LEFT JOIN users u ON b.created_by = u.id
    $whereClause
    ORDER BY b.invoice_date DESC, b.created_at DESC
    LIMIT $limit OFFSET $offset
";
$billingList = fetchAll($billingQuery, $params);

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- قائمة الفواتير -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-invoice-dollar me-2"></i>الفواتير والمحاسبة</h2>
    <a href="billing.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<?php
$totalRevenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM billing WHERE status = 'paid'")['total'];
$pendingAmount = fetchOne("SELECT COALESCE(SUM(total_amount - paid_amount), 0) as total FROM billing WHERE status IN ('pending', 'partial')")['total'];
$todayRevenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM billing WHERE DATE(invoice_date) = CURDATE() AND status = 'paid'")['total'];
$monthRevenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM billing WHERE YEAR(invoice_date) = YEAR(CURDATE()) AND MONTH(invoice_date) = MONTH(CURDATE()) AND status = 'paid'")['total'];
?>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success"><?php echo formatCurrency($totalRevenue); ?></h4>
                <p class="mb-0">إجمالي الإيرادات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?php echo formatCurrency($pendingAmount); ?></h4>
                <p class="mb-0">مبالغ معلقة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?php echo formatCurrency($todayRevenue); ?></h4>
                <p class="mb-0">إيرادات اليوم</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary"><?php echo formatCurrency($monthRevenue); ?></h4>
                <p class="mb-0">إيرادات الشهر</p>
            </div>
        </div>
    </div>
</div>

<!-- شريط البحث والفلترة -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <input type="text" class="form-control" name="search"
                       placeholder="البحث بالمريض أو رقم الفاتورة..."
                       value="<?php echo htmlspecialchars($searchTerm); ?>">
            </div>
            <div class="col-md-3">
                <select class="form-control" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                    <option value="partial" <?php echo $statusFilter === 'partial' ? 'selected' : ''; ?>>مدفوعة جزئياً</option>
                    <option value="paid" <?php echo $statusFilter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                    <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="billing.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="card">
    <div class="card-body">
        <?php if (empty($billingList)): ?>
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير</h5>
                <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                <a href="billing.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المريض</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($billingList as $bill): ?>
                        <tr class="searchable-row">
                            <td><strong><?php echo htmlspecialchars($bill['invoice_number']); ?></strong></td>
                            <td><?php echo formatDateForDisplay($bill['invoice_date']); ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($bill['patient_name']); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo htmlspecialchars($bill['patient_number']); ?></small>
                            </td>
                            <td><?php echo formatCurrency($bill['total_amount']); ?></td>
                            <td><?php echo formatCurrency($bill['paid_amount']); ?></td>
                            <td><?php echo formatCurrency($bill['total_amount'] - $bill['paid_amount']); ?></td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'pending' => 'warning',
                                    'partial' => 'info',
                                    'paid' => 'success',
                                    'cancelled' => 'danger'
                                ];
                                $statusTexts = [
                                    'pending' => 'معلقة',
                                    'partial' => 'جزئية',
                                    'paid' => 'مدفوعة',
                                    'cancelled' => 'ملغية'
                                ];
                                $statusClass = $statusClasses[$bill['status']] ?? 'secondary';
                                $statusText = $statusTexts[$bill['status']] ?? $bill['status'];
                                ?>
                                <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="billing.php?action=view&id=<?php echo $bill['id']; ?>"
                                       class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="billing.php?action=print&id=<?php echo $bill['id']; ?>"
                                       class="btn btn-sm btn-outline-success" title="طباعة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <?php if ($bill['status'] !== 'paid'): ?>
                                    <button type="button" class="btn btn-sm btn-outline-warning"
                                            data-bs-toggle="modal" data-bs-target="#paymentModal<?php echo $bill['id']; ?>" title="دفعة">
                                        <i class="fas fa-money-bill"></i>
                                    </button>
                                    <?php endif; ?>
                                    <a href="billing.php?action=edit&id=<?php echo $bill['id']; ?>"
                                       class="btn btn-sm btn-outline-primary" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if (hasPermission('admin')): ?>
                                    <a href="billing.php?action=delete&id=<?php echo $bill['id']; ?>"
                                       class="btn btn-sm btn-outline-danger btn-delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>

                        <!-- نافذة إضافة دفعة -->
                        <?php if ($bill['status'] !== 'paid'): ?>
                        <div class="modal fade" id="paymentModal<?php echo $bill['id']; ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="billing.php?action=update_payment&id=<?php echo $bill['id']; ?>">
                                        <div class="modal-header">
                                            <h5 class="modal-title">إضافة دفعة - <?php echo $bill['invoice_number']; ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">المبلغ المتبقي</label>
                                                <input type="text" class="form-control"
                                                       value="<?php echo formatCurrency($bill['total_amount'] - $bill['paid_amount']); ?>" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label for="paid_amount" class="form-label">مبلغ الدفعة *</label>
                                                <input type="number" class="form-control" name="paid_amount"
                                                       step="0.01" max="<?php echo $bill['total_amount'] - $bill['paid_amount']; ?>" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                                <select class="form-control" name="payment_method">
                                                    <option value="cash">نقداً</option>
                                                    <option value="card">بطاقة ائتمان</option>
                                                    <option value="bank_transfer">تحويل بنكي</option>
                                                    <option value="check">شيك</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <button type="submit" class="btn btn-primary">حفظ الدفعة</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>&status=<?php echo urlencode($statusFilter); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تحرير فاتورة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-file-invoice-<?php echo $action === 'add' ? 'dollar' : 'edit'; ?> me-2"></i>
        <?php echo $action === 'add' ? 'إنشاء فاتورة جديدة' : 'تحرير الفاتورة'; ?>
    </h2>
    <a href="billing.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="patient_id" class="form-label">المريض *</label>
                    <select class="form-control" id="patient_id" name="patient_id" required>
                        <option value="">اختر المريض</option>
                        <?php foreach ($patients as $patient): ?>
                        <option value="<?php echo $patient['id']; ?>"
                                <?php echo ($billing['patient_id'] ?? $_GET['patient_id'] ?? '') == $patient['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($patient['full_name'] . ' - ' . $patient['patient_number']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                    <input type="date" class="form-control" id="invoice_date" name="invoice_date"
                           value="<?php echo $billing['invoice_date'] ?? date('Y-m-d'); ?>" required>
                </div>
            </div>

            <!-- قائمة الخدمات -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-list me-2"></i>الخدمات</h5>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addServiceRow()">
                        <i class="fas fa-plus me-1"></i>إضافة خدمة
                    </button>
                </div>

                <div id="services-container">
                    <?php if (!empty($billingItems)): ?>
                        <?php foreach ($billingItems as $index => $item): ?>
                        <div class="service-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">اسم الخدمة *</label>
                                    <input type="text" class="form-control service-name"
                                           name="services[<?php echo $index; ?>][service_name]"
                                           value="<?php echo htmlspecialchars($item['service_name']); ?>"
                                           list="services-list" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الكمية *</label>
                                    <input type="number" class="form-control quantity"
                                           name="services[<?php echo $index; ?>][quantity]"
                                           value="<?php echo $item['quantity']; ?>"
                                           min="1" required onchange="calculateRowTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">السعر *</label>
                                    <input type="number" class="form-control unit-price"
                                           name="services[<?php echo $index; ?>][unit_price]"
                                           value="<?php echo $item['unit_price']; ?>"
                                           step="0.01" required onchange="calculateRowTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الإجمالي</label>
                                    <input type="text" class="form-control row-total" readonly
                                           value="<?php echo formatCurrency($item['total_price']); ?>">
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeServiceRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- صف فارغ للبداية -->
                        <div class="service-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">اسم الخدمة *</label>
                                    <input type="text" class="form-control service-name"
                                           name="services[0][service_name]"
                                           list="services-list" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الكمية *</label>
                                    <input type="number" class="form-control quantity"
                                           name="services[0][quantity]"
                                           value="1" min="1" required onchange="calculateRowTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">السعر *</label>
                                    <input type="number" class="form-control unit-price"
                                           name="services[0][unit_price]"
                                           step="0.01" required onchange="calculateRowTotal(this)">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الإجمالي</label>
                                    <input type="text" class="form-control row-total" readonly>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeServiceRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- ملخص الفاتورة -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="أي ملاحظات إضافية..."><?php echo htmlspecialchars($billing['notes'] ?? ''); ?></textarea>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">ملخص الفاتورة</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal-display">0.00 ريال</span>
                            </div>
                            <div class="mb-2">
                                <label for="discount_amount" class="form-label">الخصم:</label>
                                <input type="number" class="form-control form-control-sm"
                                       id="discount_amount" name="discount_amount"
                                       value="<?php echo $billing['discount_amount'] ?? 0; ?>"
                                       step="0.01" onchange="calculateTotal()">
                            </div>
                            <div class="mb-2">
                                <label for="tax_amount" class="form-label">الضريبة:</label>
                                <input type="number" class="form-control form-control-sm"
                                       id="tax_amount" name="tax_amount"
                                       value="<?php echo $billing['tax_amount'] ?? 0; ?>"
                                       step="0.01" onchange="calculateTotal()">
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <strong>الإجمالي:</strong>
                                <strong id="total-display">0.00 ريال</strong>
                            </div>
                            <div class="mb-2">
                                <label for="paid_amount" class="form-label">المدفوع:</label>
                                <input type="number" class="form-control form-control-sm"
                                       id="paid_amount" name="paid_amount"
                                       value="<?php echo $billing['paid_amount'] ?? 0; ?>"
                                       step="0.01">
                            </div>
                            <div class="mb-2">
                                <label for="payment_method" class="form-label">طريقة الدفع:</label>
                                <select class="form-control form-control-sm" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash" <?php echo ($billing['payment_method'] ?? '') === 'cash' ? 'selected' : ''; ?>>نقداً</option>
                                    <option value="card" <?php echo ($billing['payment_method'] ?? '') === 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                                    <option value="bank_transfer" <?php echo ($billing['payment_method'] ?? '') === 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                    <option value="check" <?php echo ($billing['payment_method'] ?? '') === 'check' ? 'selected' : ''; ?>>شيك</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    <?php echo $action === 'add' ? 'إنشاء الفاتورة' : 'حفظ التغييرات'; ?>
                </button>
                <a href="billing.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الخدمات للاقتراحات -->
<datalist id="services-list">
    <?php foreach ($services as $service): ?>
    <option value="<?php echo htmlspecialchars($service['name']); ?>" data-price="<?php echo $service['price']; ?>">
        <?php echo htmlspecialchars($service['description'] . ' - ' . formatCurrency($service['price'])); ?>
    </option>
    <?php endforeach; ?>
</datalist>

<?php endif; ?>

<?php
$inlineJS = "
let serviceIndex = " . (count($billingItems) > 0 ? count($billingItems) : 1) . ";

function addServiceRow() {
    const container = document.getElementById('services-container');
    const newRow = document.createElement('div');
    newRow.className = 'service-row border rounded p-3 mb-3';
    newRow.innerHTML = `
        <div class='row'>
            <div class='col-md-4'>
                <label class='form-label'>اسم الخدمة *</label>
                <input type='text' class='form-control service-name'
                       name='services[\${serviceIndex}][service_name]'
                       list='services-list' required>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>الكمية *</label>
                <input type='number' class='form-control quantity'
                       name='services[\${serviceIndex}][quantity]'
                       value='1' min='1' required onchange='calculateRowTotal(this)'>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>السعر *</label>
                <input type='number' class='form-control unit-price'
                       name='services[\${serviceIndex}][unit_price]'
                       step='0.01' required onchange='calculateRowTotal(this)'>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>الإجمالي</label>
                <input type='text' class='form-control row-total' readonly>
            </div>
            <div class='col-md-2 d-flex align-items-end'>
                <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeServiceRow(this)'>
                    <i class='fas fa-trash'></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newRow);
    serviceIndex++;
}

function removeServiceRow(button) {
    const container = document.getElementById('services-container');
    if (container.children.length > 1) {
        button.closest('.service-row').remove();
        calculateTotal();
    } else {
        alert('يجب أن تحتوي الفاتورة على خدمة واحدة على الأقل');
    }
}

function calculateRowTotal(element) {
    const row = element.closest('.service-row');
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
    const total = quantity * unitPrice;

    row.querySelector('.row-total').value = total.toFixed(2) + ' ريال';
    calculateTotal();
}

function calculateTotal() {
    let subtotal = 0;
    document.querySelectorAll('.service-row').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
        const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
        subtotal += quantity * unitPrice;
    });

    const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
    const total = subtotal + tax - discount;

    document.getElementById('subtotal-display').textContent = subtotal.toFixed(2) + ' ريال';
    document.getElementById('total-display').textContent = total.toFixed(2) + ' ريال';
}

// تحديث السعر عند اختيار خدمة من القائمة
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('service-name')) {
        const serviceName = e.target.value;
        const option = document.querySelector('#services-list option[value=\"' + serviceName + '\"]');
        if (option) {
            const price = option.getAttribute('data-price');
            const row = e.target.closest('.service-row');
            row.querySelector('.unit-price').value = price;
            calculateRowTotal(e.target);
        }
    }
});

// حساب الإجمالي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});

// تحديث قائمة المرضى عند البحث
$('#patient_id').select2({
    placeholder: 'ابحث عن المريض...',
    allowClear: true
});
";

include 'includes/footer.php';
?>