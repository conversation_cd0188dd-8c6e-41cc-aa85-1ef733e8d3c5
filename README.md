# نظام حكيم لإدارة العيادات الطبية
## Hakim Clinic Management System

![نظام حكيم](https://img.shields.io/badge/نظام-حكيم-blue)
![PHP](https://img.shields.io/badge/PHP-7.4+-blue)
![MySQL](https://img.shields.io/badge/MySQL-5.7+-orange)
![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3-purple)
![License](https://img.shields.io/badge/License-MIT-green)

نظام شامل لإدارة العيادات الطبية مصمم خصيصاً للبيئة العربية والسعودية، يوفر جميع الأدوات اللازمة لإدارة المرضى والمواعيد والوصفات الطبية والفواتير بكفاءة عالية.

## 🌟 المميزات الرئيسية

### 👥 إدارة المرضى
- تسجيل بيانات المرضى الكاملة
- البحث السريع والمتقدم
- تتبع التاريخ الطبي
- إدارة جهات الاتصال في الطوارئ
- رفع وإدارة الملفات الطبية

### 📅 إدارة المواعيد
- جدولة المواعيد بسهولة
- تقويم تفاعلي
- تنبيهات وإشعارات
- إدارة حالات المواعيد
- منع التعارض في المواعيد

### 💊 الوصفات الطبية
- كتابة الوصفات الإلكترونية
- قاعدة بيانات الأدوية
- طباعة الوصفات
- تتبع تاريخ الوصفات

### 💰 الفواتير والمحاسبة
- إنشاء الفواتير التلقائي
- إدارة المدفوعات
- تتبع المستحقات
- تقارير مالية شاملة

### 📊 التقارير والإحصائيات
- تقارير مالية مفصلة
- إحصائيات الأداء
- رسوم بيانية تفاعلية
- تصدير التقارير

### 👨‍⚕️ إدارة المستخدمين
- أنواع مستخدمين متعددة (مدير، طبيب، سكرتيرة)
- صلاحيات مرنة
- تتبع نشاط المستخدمين
- أمان متقدم

## 🚀 التقنيات المستخدمة

### Backend
- **PHP 7.4+** - لغة البرمجة الأساسية
- **MySQL 5.7+** - قاعدة البيانات
- **PDO** - للتفاعل الآمن مع قاعدة البيانات

### Frontend
- **HTML5 & CSS3** - هيكل وتصميم الصفحات
- **Bootstrap 5.3** - إطار عمل CSS
- **JavaScript & jQuery** - التفاعل والديناميكية
- **Chart.js** - الرسوم البيانية
- **Font Awesome** - الأيقونات
- **Cairo Font** - الخط العربي

### المكتبات والأدوات
- **SweetAlert2** - النوافذ المنبثقة
- **DataTables** - جداول البيانات التفاعلية
- **Select2** - قوائم الاختيار المحسنة
- **Bootstrap Datepicker** - منتقي التاريخ

## 📋 متطلبات النظام

### الخادم
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache**: 2.4+ مع mod_rewrite
- **مساحة القرص**: 100 ميجابايت على الأقل
- **الذاكرة**: 256 ميجابايت RAM على الأقل

### امتدادات PHP المطلوبة
- PDO
- PDO_MySQL
- mbstring
- openssl
- json
- fileinfo
- gd (للتعامل مع الصور)

## 🛠️ التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/hakim-clinic.git
cd hakim-clinic
```

### 2. إعداد قاعدة البيانات
```sql
CREATE DATABASE hakim_clinic CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hakim_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON hakim_clinic.* TO 'hakim_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تحديث إعدادات قاعدة البيانات
قم بتحرير الملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hakim_clinic');
define('DB_USER', 'hakim_user');
define('DB_PASS', 'your_password_here');
```

### 4. تشغيل التثبيت
1. افتح المتصفح واذهب إلى: `http://yoursite.com/install.php`
2. اتبع التعليمات على الشاشة
3. انقر على "بدء التثبيت"

### 5. إعداد الصلاحيات
```bash
chmod 755 uploads/
chown -R www-data:www-data /path/to/hakim/
```

## 🔐 بيانات تسجيل الدخول الافتراضية

### مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: إدارة كاملة للنظام

### طبيب تجريبي
- **اسم المستخدم**: `doctor`
- **كلمة المرور**: `doctor123`
- **الصلاحيات**: إدارة المرضى والمواعيد والوصفات

### سكرتيرة تجريبية
- **اسم المستخدم**: `secretary`
- **كلمة المرور**: `secretary123`
- **الصلاحيات**: إدارة المواعيد والمرضى

> ⚠️ **تنبيه**: تأكد من تغيير كلمات المرور الافتراضية فور التثبيت!

## 🎨 التصميم والألوان
- **اللون الرئيسي**: أزرق احترافي (#007BFF)
- **اللون الثانوي**: أبيض (#FFFFFF) 
- **اللون المساعد**: أخضر طبي (#28A745)
- **الخط العربي**: Cairo Font
- **الإطار**: Bootstrap 5 للتصميم المتجاوب

## 🔐 بيانات تسجيل الدخول الافتراضية

### مدير النظام (Admin)
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: إدارة كاملة للنظام

### طبيب تجريبي
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **الصلاحيات**: إدارة المرضى والمواعيد والوصفات

### سكرتيرة تجريبية
- **اسم المستخدم**: secretary
- **كلمة المرور**: secretary123
- **الصلاحيات**: إدارة المواعيد والمرضى (عرض فقط)

## 🚀 المتطلبات التقنية
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- Bootstrap 5
- jQuery

## 📁 هيكل المشروع
```
hakim/
├── config/          # إعدادات النظام وقاعدة البيانات
├── controllers/     # منطق التحكم (MVC)
├── models/          # نماذج البيانات
├── views/           # واجهات المستخدم
├── assets/          # CSS, JS, الصور
├── uploads/         # ملفات المرضى
├── includes/        # ملفات مشتركة
└── database/        # ملفات قاعدة البيانات
```

## ✨ الميزات الرئيسية

### 👥 إدارة المرضى
- إنشاء وتعديل ملفات المرضى
- سجل الزيارات الطبية
- رفع وإدارة التقارير والصور
- ملاحظات الطبيب الخاصة

### 🗓️ نظام المواعيد
- تقويم يومي وأسبوعي تفاعلي
- حجز المواعيد حسب التخصص
- إشعارات تذكير للمرضى
- إدارة قوائم الانتظار

### 💊 الوصفات الإلكترونية
- كتابة وصفات قابلة للطباعة
- قاعدة بيانات الأدوية
- حفظ تاريخ الوصفات
- طباعة بتنسيق احترافي

### 💳 النظام المالي
- إنشاء فواتير PDF
- تتبع المدفوعات
- تقارير مالية شاملة
- إدارة أسعار الخدمات

### 🔐 إدارة المستخدمين
- ثلاثة مستويات صلاحيات
- حماية الجلسات
- سجل العمليات
- إدارة كلمات المرور

## 📊 التقارير والإحصائيات
- تقارير يومية وشهرية
- إحصائيات المرضى
- تحليل الإيرادات
- أكثر الأدوية وصفاً

## 🛠️ التثبيت والإعداد

1. **نسخ المشروع**
```bash
git clone [repository-url]
cd hakim
```

2. **إعداد قاعدة البيانات**
- إنشاء قاعدة بيانات MySQL
- استيراد ملف `database/hakim_db.sql`
- تحديث إعدادات الاتصال في `config/database.php`

3. **إعداد الخادم**
- رفع الملفات إلى خادم الويب
- التأكد من صلاحيات مجلد `uploads/`
- تفعيل mod_rewrite في Apache

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```php
// config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hakim_clinic');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### إعدادات النظام
```php
// config/config.php
define('SITE_NAME', 'عيادة حكيم');
define('SITE_URL', 'http://localhost/hakim');
define('UPLOAD_PATH', 'uploads/');
```

## 📱 الدعم والتوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام

## 🔒 الأمان
- حماية من SQL Injection
- تشفير كلمات المرور
- حماية الجلسات
- تحقق من صحة البيانات المدخلة

## 📞 الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

---
**تم تطوير النظام بواسطة**: فريق تطوير حكيم  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2024
