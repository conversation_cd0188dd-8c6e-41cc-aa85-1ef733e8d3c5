# نظام إدارة العيادات الطبية - حكيم

## 📋 وصف المشروع
نظام شامل لإدارة العيادات الطبية باللغة العربية مع دعم كامل للمرضى والمواعيد والوصفات الطبية والفواتير.

## 🎨 التصميم والألوان
- **اللون الرئيسي**: أزرق احترافي (#007BFF)
- **اللون الثانوي**: أبيض (#FFFFFF) 
- **اللون المساعد**: أخضر طبي (#28A745)
- **الخط العربي**: Cairo Font
- **الإطار**: Bootstrap 5 للتصميم المتجاوب

## 🔐 بيانات تسجيل الدخول الافتراضية

### مدير النظام (Admin)
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: إدارة كاملة للنظام

### طبيب تجريبي
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **الصلاحيات**: إدارة المرضى والمواعيد والوصفات

### سكرتيرة تجريبية
- **اسم المستخدم**: secretary
- **كلمة المرور**: secretary123
- **الصلاحيات**: إدارة المواعيد والمرضى (عرض فقط)

## 🚀 المتطلبات التقنية
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- Bootstrap 5
- jQuery

## 📁 هيكل المشروع
```
hakim/
├── config/          # إعدادات النظام وقاعدة البيانات
├── controllers/     # منطق التحكم (MVC)
├── models/          # نماذج البيانات
├── views/           # واجهات المستخدم
├── assets/          # CSS, JS, الصور
├── uploads/         # ملفات المرضى
├── includes/        # ملفات مشتركة
└── database/        # ملفات قاعدة البيانات
```

## ✨ الميزات الرئيسية

### 👥 إدارة المرضى
- إنشاء وتعديل ملفات المرضى
- سجل الزيارات الطبية
- رفع وإدارة التقارير والصور
- ملاحظات الطبيب الخاصة

### 🗓️ نظام المواعيد
- تقويم يومي وأسبوعي تفاعلي
- حجز المواعيد حسب التخصص
- إشعارات تذكير للمرضى
- إدارة قوائم الانتظار

### 💊 الوصفات الإلكترونية
- كتابة وصفات قابلة للطباعة
- قاعدة بيانات الأدوية
- حفظ تاريخ الوصفات
- طباعة بتنسيق احترافي

### 💳 النظام المالي
- إنشاء فواتير PDF
- تتبع المدفوعات
- تقارير مالية شاملة
- إدارة أسعار الخدمات

### 🔐 إدارة المستخدمين
- ثلاثة مستويات صلاحيات
- حماية الجلسات
- سجل العمليات
- إدارة كلمات المرور

## 📊 التقارير والإحصائيات
- تقارير يومية وشهرية
- إحصائيات المرضى
- تحليل الإيرادات
- أكثر الأدوية وصفاً

## 🛠️ التثبيت والإعداد

1. **نسخ المشروع**
```bash
git clone [repository-url]
cd hakim
```

2. **إعداد قاعدة البيانات**
- إنشاء قاعدة بيانات MySQL
- استيراد ملف `database/hakim_db.sql`
- تحديث إعدادات الاتصال في `config/database.php`

3. **إعداد الخادم**
- رفع الملفات إلى خادم الويب
- التأكد من صلاحيات مجلد `uploads/`
- تفعيل mod_rewrite في Apache

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```php
// config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hakim_clinic');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### إعدادات النظام
```php
// config/config.php
define('SITE_NAME', 'عيادة حكيم');
define('SITE_URL', 'http://localhost/hakim');
define('UPLOAD_PATH', 'uploads/');
```

## 📱 الدعم والتوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام

## 🔒 الأمان
- حماية من SQL Injection
- تشفير كلمات المرور
- حماية الجلسات
- تحقق من صحة البيانات المدخلة

## 📞 الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

---
**تم تطوير النظام بواسطة**: فريق تطوير حكيم  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2024
