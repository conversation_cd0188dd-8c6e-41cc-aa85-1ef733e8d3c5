<?php
/**
 * صفحة تسجيل الدخول - نظام حكيم لإدارة العيادات
 * Login Page - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// إعادة توجيه المستخدم إذا كان مسجل دخول بالفعل
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // البحث عن المستخدم
            $query = "SELECT * FROM users WHERE username = ? AND is_active = 1";
            $user = fetchOne($query, [$username]);
            
            if ($user && verifyPassword($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['user_type'] = $user['user_type'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['login_time'] = time();
                
                // تحديث آخر تسجيل دخول
                $updateQuery = "UPDATE users SET last_login = NOW() WHERE id = ?";
                executeQuery($updateQuery, [$user['id']]);
                
                // تسجيل العملية في سجل النشاط
                logActivity($user['id'], 'تسجيل دخول', 'users', $user['id']);
                
                // إعداد تذكرني
                if ($remember_me) {
                    $token = generateRandomToken();
                    setcookie('remember_token', $token, time() + REMEMBER_ME_DURATION, '/', '', true, true);
                    
                    // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول للرموز لاحقاً)
                }
                
                // إعادة التوجيه إلى لوحة التحكم
                redirect('dashboard.php');
            } else {
                $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // تسجيل محاولة دخول فاشلة
                logActivity(null, 'محاولة دخول فاشلة', 'users', null, null, ['username' => $username]);
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام. يرجى المحاولة لاحقاً';
            error_log("Login error: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام حكيم لإدارة العيادات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo PRIMARY_COLOR; ?>;
            --secondary-color: <?php echo SECONDARY_COLOR; ?>;
            --success-color: <?php echo SUCCESS_COLOR; ?>;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: var(--secondary-color);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #6c757d;
            margin-bottom: 0;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .login-info {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-info h2 {
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .login-info p {
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .demo-accounts {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .demo-accounts h5 {
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .demo-account {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: right;
        }
        
        .demo-account:last-child {
            margin-bottom: 0;
        }
        
        .demo-account strong {
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .demo-account small {
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
            }
            
            .login-form,
            .login-info {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- نموذج تسجيل الدخول -->
                <div class="col-lg-6">
                    <div class="login-form">
                        <div class="login-header">
                            <i class="fas fa-stethoscope fa-3x text-primary mb-3"></i>
                            <h1>نظام حكيم</h1>
                            <p>نظام إدارة العيادات الطبية</p>
                        </div>
                        
                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success_message)): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم المستخدم
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال كلمة المرور
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    تذكرني
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام وبيانات التجربة -->
                <div class="col-lg-6">
                    <div class="login-info">
                        <h2>مرحباً بك في نظام حكيم</h2>
                        <p>نظام شامل لإدارة العيادات الطبية يوفر جميع الأدوات اللازمة لإدارة المرضى والمواعيد والوصفات الطبية والفواتير بكفاءة عالية.</p>
                        
                        <div class="demo-accounts">
                            <h5><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول التجريبية</h5>
                            
                            <div class="demo-account">
                                <strong><i class="fas fa-user-shield me-2"></i>مدير النظام</strong>
                                <div>اسم المستخدم: <code>admin</code></div>
                                <div>كلمة المرور: <code>admin123</code></div>
                                <small>صلاحيات كاملة لإدارة النظام</small>
                            </div>
                            
                            <div class="demo-account">
                                <strong><i class="fas fa-user-md me-2"></i>طبيب</strong>
                                <div>اسم المستخدم: <code>doctor</code></div>
                                <div>كلمة المرور: <code>doctor123</code></div>
                                <small>إدارة المرضى والمواعيد والوصفات</small>
                            </div>
                            
                            <div class="demo-account">
                                <strong><i class="fas fa-user-tie me-2"></i>سكرتيرة</strong>
                                <div>اسم المستخدم: <code>secretary</code></div>
                                <div>كلمة المرور: <code>secretary123</code></div>
                                <small>إدارة المواعيد والمرضى</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة نسيان كلمة المرور -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">استعادة كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>يرجى التواصل مع مدير النظام لاستعادة كلمة المرور.</p>
                    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>الهاتف:</strong> 0501234567</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // التحقق من صحة النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // تركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
    </script>
</body>
</html>
