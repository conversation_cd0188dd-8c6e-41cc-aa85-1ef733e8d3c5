/**
 * نظام حكيم لإدارة العيادات - ملف JavaScript الرئيسي
 * Hakim Clinic Management System - Main JavaScript File
 */

// إعدادات عامة
const HakimSystem = {
    // إعدادات AJAX
    ajaxSettings: {
        timeout: 30000,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    },
    
    // رسائل النظام
    messages: {
        success: 'تم بنجاح',
        error: 'حدث خطأ',
        confirm: 'هل أنت متأكد؟',
        loading: 'جاري التحميل...',
        noData: 'لا توجد بيانات',
        deleteConfirm: 'هل تريد حذف هذا العنصر؟',
        saveSuccess: 'تم الحفظ بنجاح',
        updateSuccess: 'تم التحديث بنجاح',
        deleteSuccess: 'تم الحذف بنجاح'
    }
};

// دالة التهيئة عند تحميل الصفحة
$(document).ready(function() {
    initializeSystem();
    setupEventHandlers();
    setupFormValidation();
    setupDataTables();
    setupDatePickers();
    setupTooltips();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // إضافة تأثيرات الحركة
    $('.fade-in').each(function(index) {
        $(this).delay(index * 100).queue(function() {
            $(this).addClass('animate__animated animate__fadeInUp').dequeue();
        });
    });
    
    // تحديث الوقت كل ثانية
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // إعداد الشريط الجانبي للموبايل
    setupMobileSidebar();
}

/**
 * إعداد معالجات الأحداث
 */
function setupEventHandlers() {
    // زر الحذف مع التأكيد
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();
        const url = $(this).attr('href') || $(this).data('url');
        confirmDelete(url);
    });
    
    // زر الطباعة
    $(document).on('click', '.btn-print', function() {
        window.print();
    });
    
    // زر التصدير
    $(document).on('click', '.btn-export', function() {
        const format = $(this).data('format') || 'pdf';
        const url = $(this).data('url');
        exportData(url, format);
    });
    
    // البحث السريع
    $('#quick-search').on('keyup', function() {
        const searchTerm = $(this).val();
        performQuickSearch(searchTerm);
    });
    
    // تحديث تلقائي للإشعارات
    setInterval(checkNotifications, 30000); // كل 30 ثانية
}

/**
 * إعداد التحقق من صحة النماذج
 */
function setupFormValidation() {
    // التحقق من النماذج
    $('form').each(function() {
        $(this).validate({
            errorClass: 'is-invalid',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function(element) {
                $(element).addClass('is-invalid').removeClass('is-valid');
            },
            unhighlight: function(element) {
                $(element).addClass('is-valid').removeClass('is-invalid');
            }
        });
    });
    
    // التحقق من رقم الهاتف السعودي
    $.validator.addMethod('saudiPhone', function(value, element) {
        return this.optional(element) || /^(05|5)[0-9]{8}$/.test(value);
    }, 'يرجى إدخال رقم هاتف سعودي صحيح');
    
    // التحقق من الهوية الوطنية
    $.validator.addMethod('saudiId', function(value, element) {
        return this.optional(element) || /^[12][0-9]{9}$/.test(value);
    }, 'يرجى إدخال رقم هوية وطنية صحيح');
}

/**
 * إعداد جداول البيانات
 */
function setupDataTables() {
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }
}

/**
 * إعداد منتقي التاريخ
 */
function setupDatePickers() {
    if ($.fn.datepicker) {
        $('.date-picker').datepicker({
            format: 'dd/mm/yyyy',
            language: 'ar',
            autoclose: true,
            todayHighlight: true,
            orientation: 'bottom right'
        });
        
        $('.datetime-picker').datetimepicker({
            format: 'dd/mm/yyyy hh:ii',
            language: 'ar',
            autoclose: true,
            todayHighlight: true
        });
    }
}

/**
 * إعداد التلميحات
 */
function setupTooltips() {
    $('[data-bs-toggle="tooltip"]').tooltip();
    $('[data-bs-toggle="popover"]').popover();
}

/**
 * إعداد الشريط الجانبي للموبايل
 */
function setupMobileSidebar() {
    $('#sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });
    
    // إغلاق الشريط الجانبي عند النقر خارجه
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.sidebar, #sidebar-toggle').length) {
            $('.sidebar').removeClass('show');
        }
    });
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Riyadh'
    };
    
    const dateTimeString = now.toLocaleDateString('ar-SA', options);
    $('#current-datetime').text(dateTimeString);
}

/**
 * تأكيد الحذف
 */
function confirmDelete(url) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: HakimSystem.messages.deleteConfirm,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            performDelete(url);
        }
    });
}

/**
 * تنفيذ عملية الحذف
 */
function performDelete(url) {
    showLoading();
    
    $.ajax({
        url: url,
        type: 'POST',
        data: { _method: 'DELETE' },
        success: function(response) {
            hideLoading();
            if (response.success) {
                showSuccess(HakimSystem.messages.deleteSuccess);
                setTimeout(() => location.reload(), 1500);
            } else {
                showError(response.message || HakimSystem.messages.error);
            }
        },
        error: function() {
            hideLoading();
            showError(HakimSystem.messages.error);
        }
    });
}

/**
 * البحث السريع
 */
function performQuickSearch(searchTerm) {
    if (searchTerm.length < 2) return;
    
    $.ajax({
        url: 'search.php',
        type: 'GET',
        data: { q: searchTerm },
        success: function(response) {
            displaySearchResults(response);
        }
    });
}

/**
 * فحص الإشعارات
 */
function checkNotifications() {
    $.ajax({
        url: 'notifications.php',
        type: 'GET',
        success: function(response) {
            updateNotificationBadge(response.count);
            if (response.notifications && response.notifications.length > 0) {
                displayNotifications(response.notifications);
            }
        }
    });
}

/**
 * تصدير البيانات
 */
function exportData(url, format) {
    showLoading();
    window.open(`${url}?format=${format}`, '_blank');
    hideLoading();
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'نجح!',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'خطأ!',
        text: message
    });
}

/**
 * عرض شاشة التحميل
 */
function showLoading() {
    Swal.fire({
        title: HakimSystem.messages.loading,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoading() {
    Swal.close();
}

/**
 * تنسيق الأرقام العربية
 */
function formatArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.replace(/[0-9]/g, function(w) {
        return arabicNumbers[+w];
    });
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone(phone) {
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    return phoneRegex.test(phone);
}
