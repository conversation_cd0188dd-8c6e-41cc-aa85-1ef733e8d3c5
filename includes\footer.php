            </main>
        </div>
    </div>
    
    <!-- الفوتر -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-start">
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> نظام حكيم لإدارة العيادات. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        الإصدار <?php echo SITE_VERSION; ?> | 
                        <a href="mailto:<EMAIL>" class="text-decoration-none">الدعم الفني</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- jQuery Validation -->
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Chart.js للتقارير والإحصائيات -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Moment.js للتعامل مع التواريخ -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    
    <!-- Bootstrap Datepicker -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo getFullUrl('assets/js/main.js'); ?>"></script>
    
    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript -->
    <?php if (isset($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>
    
    <script>
        // إعدادات خاصة بالصفحة الحالية
        $(document).ready(function() {
            // تحديث التاريخ والوقت كل ثانية
            function updateDateTime() {
                const now = new Date();
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    timeZone: 'Asia/Riyadh'
                };
                
                const dateTimeString = now.toLocaleDateString('ar-SA', options);
                $('#datetime-text').text(dateTimeString);
            }
            
            // تحديث الوقت فوراً ثم كل ثانية
            updateDateTime();
            setInterval(updateDateTime, 1000);
            
            // إعداد التلميحات
            $('[data-bs-toggle="tooltip"]').tooltip();
            
            // إعداد النوافذ المنبثقة
            $('[data-bs-toggle="popover"]').popover();
            
            // تأكيد الحذف
            $('.btn-delete').on('click', function(e) {
                e.preventDefault();
                const url = $(this).attr('href');
                
                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            });
            
            // البحث السريع
            $('#quick-search').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('.searchable-row').each(function() {
                    const text = $(this).text().toLowerCase();
                    $(this).toggle(text.indexOf(searchTerm) > -1);
                });
            });
            
            // تحديد الكل في الجداول
            $('.select-all').on('change', function() {
                const isChecked = $(this).is(':checked');
                $(this).closest('table').find('.select-item').prop('checked', isChecked);
            });
            
            // تحديث عداد العناصر المحددة
            $('.select-item').on('change', function() {
                const table = $(this).closest('table');
                const totalItems = table.find('.select-item').length;
                const selectedItems = table.find('.select-item:checked').length;
                
                // تحديث حالة "تحديد الكل"
                const selectAll = table.find('.select-all');
                selectAll.prop('checked', selectedItems === totalItems);
                selectAll.prop('indeterminate', selectedItems > 0 && selectedItems < totalItems);
                
                // إظهار/إخفاء أزرار العمليات المجمعة
                const bulkActions = $('.bulk-actions');
                if (selectedItems > 0) {
                    bulkActions.removeClass('d-none');
                    bulkActions.find('.selected-count').text(selectedItems);
                } else {
                    bulkActions.addClass('d-none');
                }
            });
            
            // طباعة الصفحة
            $('.btn-print').on('click', function() {
                window.print();
            });
            
            // تصدير البيانات
            $('.btn-export').on('click', function() {
                const format = $(this).data('format');
                const url = $(this).data('url');
                
                if (url) {
                    window.open(url + '?format=' + format, '_blank');
                }
            });
            
            // إعداد التحقق من النماذج
            $('form').each(function() {
                const form = $(this);
                
                // إضافة قواعد التحقق المخصصة
                if (form.hasClass('needs-validation')) {
                    form.on('submit', function(e) {
                        if (!this.checkValidity()) {
                            e.preventDefault();
                            e.stopPropagation();
                        }
                        form.addClass('was-validated');
                    });
                }
            });
            
            // تحسين تجربة المستخدم للنماذج الطويلة
            $('.form-step').each(function(index) {
                if (index > 0) {
                    $(this).hide();
                }
            });
            
            $('.btn-next-step').on('click', function() {
                const currentStep = $(this).closest('.form-step');
                const nextStep = currentStep.next('.form-step');
                
                if (nextStep.length) {
                    currentStep.hide();
                    nextStep.show();
                }
            });
            
            $('.btn-prev-step').on('click', function() {
                const currentStep = $(this).closest('.form-step');
                const prevStep = currentStep.prev('.form-step');
                
                if (prevStep.length) {
                    currentStep.hide();
                    prevStep.show();
                }
            });
            
            // إعداد الإشعارات التلقائية
            <?php if (isset($_SESSION['success_message'])): ?>
                Swal.fire({
                    icon: 'success',
                    title: 'نجح!',
                    text: '<?php echo $_SESSION['success_message']; ?>',
                    timer: 3000,
                    showConfirmButton: false
                });
                <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error_message'])): ?>
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: '<?php echo $_SESSION['error_message']; ?>'
                });
                <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['warning_message'])): ?>
                Swal.fire({
                    icon: 'warning',
                    title: 'تنبيه!',
                    text: '<?php echo $_SESSION['warning_message']; ?>'
                });
                <?php unset($_SESSION['warning_message']); ?>
            <?php endif; ?>
        });
    </script>
</body>
</html>
