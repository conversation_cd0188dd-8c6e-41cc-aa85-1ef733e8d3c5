<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="نظام حكيم لإدارة العيادات الطبية - نظام شامل لإدارة المرضى والمواعيد والوصفات الطبية">
    <meta name="keywords" content="عيادة, طبيب, مرضى, مواعيد, وصفات طبية, إدارة عيادات">
    <meta name="author" content="فريق تطوير حكيم">
    
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>نظام حكيم لإدارة العيادات</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo getFullUrl('assets/images/favicon.ico'); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Cairo Font من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo getFullUrl('assets/css/style.css'); ?>" rel="stylesheet">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        /* إعدادات إضافية للألوان المخصصة */
        :root {
            --primary-color: <?php echo PRIMARY_COLOR; ?>;
            --secondary-color: <?php echo SECONDARY_COLOR; ?>;
            --success-color: <?php echo SUCCESS_COLOR; ?>;
            --danger-color: <?php echo DANGER_COLOR; ?>;
            --warning-color: <?php echo WARNING_COLOR; ?>;
            --info-color: <?php echo INFO_COLOR; ?>;
        }
        
        /* تخصيص شريط التمرير */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <!-- شعار النظام -->
            <a class="navbar-brand d-flex align-items-center" href="<?php echo getFullUrl('dashboard.php'); ?>">
                <i class="fas fa-stethoscope me-2"></i>
                <span>نظام حكيم</span>
            </a>
            
            <!-- زر القائمة للموبايل -->
            <button class="navbar-toggler" type="button" id="sidebar-toggle">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- عناصر شريط التنقل -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- التاريخ والوقت الحالي -->
                    <li class="nav-item">
                        <span class="nav-link" id="current-datetime">
                            <i class="fas fa-clock me-1"></i>
                            <span id="datetime-text"><?php echo date('d/m/Y H:i'); ?></span>
                        </span>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count">
                                3
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-calendar-check text-success"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-bold">موعد جديد</div>
                                            <div class="small text-muted">محمد أحمد - 10:00 ص</div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="notifications.php">عرض جميع الإشعارات</a></li>
                        </ul>
                    </li>
                    
                    <!-- ملف المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <img src="<?php echo getFullUrl('assets/images/default-avatar.png'); ?>" alt="صورة المستخدم" class="rounded-circle me-2" width="32" height="32">
                            <span><?php echo $_SESSION['full_name'] ?? 'المستخدم'; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header"><?php echo $_SESSION['full_name'] ?? 'المستخدم'; ?></h6></li>
                            <li><small class="dropdown-item-text text-muted"><?php echo $_SESSION['user_type'] ?? ''; ?></small></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- الحاوي الرئيسي -->
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <!-- لوحة التحكم -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        
                        <!-- إدارة المرضى -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'patients.php') ? 'active' : ''; ?>" href="patients.php">
                                <i class="fas fa-users"></i>
                                إدارة المرضى
                            </a>
                        </li>
                        
                        <!-- المواعيد -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'appointments.php') ? 'active' : ''; ?>" href="appointments.php">
                                <i class="fas fa-calendar-alt"></i>
                                المواعيد
                            </a>
                        </li>
                        
                        <!-- الوصفات الطبية -->
                        <?php if (hasPermission('doctor')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'prescriptions.php') ? 'active' : ''; ?>" href="prescriptions.php">
                                <i class="fas fa-prescription-bottle-alt"></i>
                                الوصفات الطبية
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <!-- الفواتير -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'billing.php') ? 'active' : ''; ?>" href="billing.php">
                                <i class="fas fa-file-invoice-dollar"></i>
                                الفواتير والمحاسبة
                            </a>
                        </li>
                        
                        <!-- التقارير -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'reports.php') ? 'active' : ''; ?>" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير والإحصائيات
                            </a>
                        </li>
                        
                        <!-- إعدادات النظام (للمدير فقط) -->
                        <?php if (hasPermission('admin')): ?>
                        <li class="nav-item">
                            <hr class="my-3">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'users.php') ? 'active' : ''; ?>" href="users.php">
                                <i class="fas fa-user-cog"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'services.php') ? 'active' : ''; ?>" href="services.php">
                                <i class="fas fa-cogs"></i>
                                إدارة الخدمات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'medications.php') ? 'active' : ''; ?>" href="medications.php">
                                <i class="fas fa-pills"></i>
                                إدارة الأدوية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'settings.php') ? 'active' : ''; ?>" href="settings.php">
                                <i class="fas fa-cog"></i>
                                إعدادات النظام
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
            
            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <?php if (isset($pageTitle)): ?>
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $pageTitle; ?></h1>
                    <?php if (isset($pageActions)): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php echo $pageActions; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
