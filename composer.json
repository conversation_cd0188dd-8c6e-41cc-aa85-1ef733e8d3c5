{"name": "hakim/clinic-management-system", "description": "نظام حكيم لإدارة العيادات الطبية - نظام شامل لإدارة المرضى والمواعيد والوصفات الطبية والفواتير", "type": "project", "keywords": ["clinic", "medical", "management", "php", "mysql", "arabic"], "homepage": "https://hakimclinic.com", "license": "MIT", "authors": [{"name": "فريق تطوير حكيم", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-json": "*", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "suggest": {"ext-gd": "Required for image processing", "phpmailer/phpmailer": "For advanced email functionality", "dompdf/dompdf": "For PDF generation", "mpdf/mpdf": "Alternative PDF library with better Arabic support"}, "autoload": {"psr-4": {"Hakim\\": "src/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"Hakim\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "install-db": "php install.php", "backup": "php scripts/backup.php", "clear-cache": "php scripts/clear-cache.php"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "minimum-stability": "stable", "prefer-stable": true, "archive": {"exclude": ["/tests", "/docs", "/.git", "/.giti<PERSON>re", "/phpunit.xml", "/composer.lock"]}}