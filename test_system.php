<?php
/**
 * اختبار النظام - نظام حكيم لإدارة العيادات
 * System Test - Hakim Clinic Management System
 */

// التحقق من متطلبات النظام
$requirements = [
    'PHP Version' => [
        'required' => '7.4.0',
        'current' => PHP_VERSION,
        'status' => version_compare(PHP_VERSION, '7.4.0', '>=')
    ],
    'PDO Extension' => [
        'required' => 'Enabled',
        'current' => extension_loaded('pdo') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('pdo')
    ],
    'PDO MySQL' => [
        'required' => 'Enabled',
        'current' => extension_loaded('pdo_mysql') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('pdo_mysql')
    ],
    'mbstring Extension' => [
        'required' => 'Enabled',
        'current' => extension_loaded('mbstring') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('mbstring')
    ],
    'OpenSSL Extension' => [
        'required' => 'Enabled',
        'current' => extension_loaded('openssl') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('openssl')
    ],
    'JSON Extension' => [
        'required' => 'Enabled',
        'current' => extension_loaded('json') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('json')
    ],
    'FileInfo Extension' => [
        'required' => 'Enabled',
        'current' => extension_loaded('fileinfo') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('fileinfo')
    ]
];

// التحقق من الملفات المطلوبة
$files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php',
    'assets/css/style.css',
    'assets/js/main.js',
    'database/hakim_db.sql'
];

// التحقق من المجلدات المطلوبة
$directories = [
    'config',
    'controllers',
    'models',
    'views',
    'assets',
    'uploads',
    'includes',
    'database'
];

// التحقق من صلاحيات الكتابة
$writableDirectories = [
    'uploads',
    '.' // للجلسات
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - نظام حكيم لإدارة العيادات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .status-pass {
            color: #28a745;
        }
        
        .status-fail {
            color: #dc3545;
        }
        
        .status-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <i class="fas fa-stethoscope fa-3x text-primary mb-3"></i>
                <h1 class="text-primary">اختبار نظام حكيم</h1>
                <p class="text-muted">فحص متطلبات النظام والملفات المطلوبة</p>
            </div>
            
            <!-- متطلبات PHP -->
            <div class="test-section">
                <h4><i class="fas fa-code me-2"></i>متطلبات PHP</h4>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المتطلب</th>
                                <th>المطلوب</th>
                                <th>الحالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($requirements as $name => $req): ?>
                            <tr>
                                <td><?php echo $name; ?></td>
                                <td><?php echo $req['required']; ?></td>
                                <td><?php echo $req['current']; ?></td>
                                <td>
                                    <?php if ($req['status']): ?>
                                        <i class="fas fa-check-circle status-pass"></i> نجح
                                    <?php else: ?>
                                        <i class="fas fa-times-circle status-fail"></i> فشل
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- الملفات المطلوبة -->
            <div class="test-section">
                <h4><i class="fas fa-file me-2"></i>الملفات المطلوبة</h4>
                <div class="row">
                    <?php foreach ($files as $file): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <?php if (file_exists($file)): ?>
                                <i class="fas fa-check-circle status-pass me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-times-circle status-fail me-2"></i>
                            <?php endif; ?>
                            <span><?php echo $file; ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- المجلدات المطلوبة -->
            <div class="test-section">
                <h4><i class="fas fa-folder me-2"></i>المجلدات المطلوبة</h4>
                <div class="row">
                    <?php foreach ($directories as $dir): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <?php if (is_dir($dir)): ?>
                                <i class="fas fa-check-circle status-pass me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-times-circle status-fail me-2"></i>
                            <?php endif; ?>
                            <span><?php echo $dir; ?>/</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- صلاحيات الكتابة -->
            <div class="test-section">
                <h4><i class="fas fa-lock me-2"></i>صلاحيات الكتابة</h4>
                <div class="row">
                    <?php foreach ($writableDirectories as $dir): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <?php if (is_writable($dir)): ?>
                                <i class="fas fa-check-circle status-pass me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-times-circle status-fail me-2"></i>
                            <?php endif; ?>
                            <span><?php echo $dir; ?>/</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- اختبار قاعدة البيانات -->
            <div class="test-section">
                <h4><i class="fas fa-database me-2"></i>اختبار قاعدة البيانات</h4>
                <?php
                try {
                    require_once 'config/database.php';
                    echo '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>تم الاتصال بقاعدة البيانات بنجاح</div>';
                    
                    // التحقق من وجود الجداول
                    $tables = ['users', 'patients', 'appointments', 'prescriptions', 'billing'];
                    $existingTables = [];
                    
                    foreach ($tables as $table) {
                        $query = "SHOW TABLES LIKE '$table'";
                        $result = fetchOne($query);
                        if ($result) {
                            $existingTables[] = $table;
                        }
                    }
                    
                    if (count($existingTables) === count($tables)) {
                        echo '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>جميع الجداول المطلوبة موجودة</div>';
                    } else {
                        echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>بعض الجداول مفقودة. يرجى تشغيل التثبيت.</div>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage() . '</div>';
                }
                ?>
            </div>
            
            <!-- معلومات النظام -->
            <div class="test-section">
                <h4><i class="fas fa-info-circle me-2"></i>معلومات النظام</h4>
                <div class="row">
                    <div class="col-md-6">
                        <strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?><br>
                        <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?><br>
                        <strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?><br>
                        <strong>حد رفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?><br>
                        <strong>وقت التنفيذ الأقصى:</strong> <?php echo ini_get('max_execution_time'); ?>s
                    </div>
                </div>
            </div>
            
            <!-- الخطوات التالية -->
            <div class="test-section">
                <h4><i class="fas fa-arrow-right me-2"></i>الخطوات التالية</h4>
                <div class="d-grid gap-2">
                    <?php
                    $allRequirementsMet = true;
                    foreach ($requirements as $req) {
                        if (!$req['status']) {
                            $allRequirementsMet = false;
                            break;
                        }
                    }
                    
                    if ($allRequirementsMet): ?>
                        <a href="install.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-download me-2"></i>بدء التثبيت
                        </a>
                        <a href="login.php" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول (إذا كان النظام مثبت)
                        </a>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            يرجى حل المشاكل المذكورة أعلاه قبل المتابعة
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
