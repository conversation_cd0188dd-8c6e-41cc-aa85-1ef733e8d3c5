# نظام حكيم لإدارة العيادات - إعدادات Apache
# Hakim Clinic Management System - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.php~">
    Order allow,deny
    Deny from all
</Files>

<Files "*.inc">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# حماية مجلد الإعدادات
<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

# حماية مجلد قاعدة البيانات
<Directory "database">
    Order allow,deny
    Deny from all
</Directory>

# السماح بالوصول لملفات الرفع مع قيود
<Directory "uploads">
    Options -Indexes
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php3">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php4">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php5">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.phtml">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.pl">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.py">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.jsp">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.asp">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.sh">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.cgi">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# إعدادات الأمان
ServerSignature Off

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/error.log
</IfModule>

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^$ index.php [L]

# إعادة توجيه الروابط الودية (يمكن تطويرها لاحقاً)
# RewriteRule ^patient/([0-9]+)/?$ patients.php?view=$1 [L,QSA]
# RewriteRule ^appointment/([0-9]+)/?$ appointments.php?view=$1 [L,QSA]

# منع الوصول المباشر لملفات PHP في مجلدات معينة
RewriteRule ^(includes|config|database)/.* - [F,L]

# إعادة توجيه HTTP إلى HTTPS (اختياري - يفعل في الإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# معالجة أخطاء 404
ErrorDocument 404 /404.php

# معالجة أخطاء 403
ErrorDocument 403 /403.php

# معالجة أخطاء 500
ErrorDocument 500 /500.php
