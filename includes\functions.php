<?php
/**
 * ملف الدوال المساعدة - نظام حكيم لإدارة العيادات
 * Helper Functions - Hakim Clinic Management System
 */

/**
 * بدء الجلسة بشكل آمن
 */
function startSecureSession() {
    if (session_status() == PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_only_cookies', 1);
        session_start();
    }
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من صلاحيات المستخدم
 */
function hasPermission($requiredRole) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $userRole = $_SESSION['user_type'] ?? '';
    
    // المدير له صلاحية الوصول لكل شيء
    if ($userRole === 'admin') {
        return true;
    }
    
    // التحقق من الصلاحيات المحددة
    $permissions = [
        'admin' => ['admin'],
        'doctor' => ['admin', 'doctor'],
        'secretary' => ['admin', 'doctor', 'secretary']
    ];
    
    return in_array($userRole, $permissions[$requiredRole] ?? []);
}

/**
 * إعادة توجيه المستخدم
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(05|5)[0-9]{8}$/', $phone);
}

/**
 * التحقق من صحة الهوية الوطنية السعودية
 */
function isValidSaudiId($id) {
    $id = preg_replace('/[^0-9]/', '', $id);
    return preg_match('/^[12][0-9]{9}$/', $id) && strlen($id) === 10;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * توليد رمز عشوائي
 */
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDateForDisplay($date, $format = 'd/m/Y') {
    if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
        return '';
    }
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت للعرض
 */
function formatDateTimeForDisplay($datetime, $format = 'd/m/Y H:i') {
    return formatDateForDisplay($datetime, $format);
}

/**
 * تنسيق المبلغ المالي
 */
function formatCurrency($amount, $currency = 'ريال') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * تحويل التاريخ من التنسيق العربي إلى MySQL
 */
function convertArabicDateToMysql($arabicDate) {
    if (empty($arabicDate)) return null;
    
    $parts = explode('/', $arabicDate);
    if (count($parts) === 3) {
        return $parts[2] . '-' . str_pad($parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($parts[0], 2, '0', STR_PAD_LEFT);
    }
    return null;
}

/**
 * رفع ملف بشكل آمن
 */
function uploadFile($file, $uploadDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmpName = $file['tmp_name'];
    $fileError = $file['error'];
    
    // التحقق من وجود خطأ في الرفع
    if ($fileError !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    // التحقق من حجم الملف
    if ($fileSize > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    // إنشاء اسم ملف فريد
    $newFileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $uploadPath = $uploadDir . '/' . $newFileName;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // نقل الملف
    if (move_uploaded_file($fileTmpName, $uploadPath)) {
        return [
            'success' => true,
            'filename' => $newFileName,
            'original_name' => $fileName,
            'path' => $uploadPath,
            'size' => $fileSize,
            'type' => $fileExtension
        ];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * حذف ملف بشكل آمن
 */
function deleteFile($filePath) {
    if (file_exists($filePath) && is_file($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * تسجيل العمليات في سجل النشاط
 */
function logActivity($userId, $action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
    try {
        $query = "INSERT INTO activity_log (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $userId,
            $action,
            $tableName,
            $recordId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        executeQuery($query, $params);
    } catch (Exception $e) {
        error_log("Error logging activity: " . $e->getMessage());
    }
}

/**
 * إرسال إشعار بالبريد الإلكتروني
 */
function sendEmailNotification($to, $subject, $message, $isHtml = true) {
    // يمكن تطوير هذه الدالة لاحقاً لاستخدام مكتبة PHPMailer
    $headers = [
        'From: ' . FROM_EMAIL,
        'Reply-To: ' . FROM_EMAIL,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    if ($isHtml) {
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: text/html; charset=UTF-8';
    }
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * إرسال إشعار SMS
 */
function sendSMSNotification($phone, $message) {
    // يمكن تطوير هذه الدالة لاحقاً لاستخدام خدمة SMS
    // مثل Twilio أو خدمة SMS محلية
    return true; // مؤقتاً
}

/**
 * توليد رقم فاتورة فريد
 */
function generateInvoiceNumber() {
    $prefix = 'INV';
    $year = date('Y');
    $month = date('m');
    
    // البحث عن آخر رقم فاتورة في الشهر الحالي
    $query = "SELECT MAX(CAST(SUBSTRING(invoice_number, 8) AS UNSIGNED)) as last_number 
              FROM billing 
              WHERE invoice_number LIKE ? 
              AND YEAR(invoice_date) = ? 
              AND MONTH(invoice_date) = ?";
    
    $pattern = $prefix . $year . $month . '%';
    $result = fetchOne($query, [$pattern, $year, $month]);
    
    $nextNumber = ($result['last_number'] ?? 0) + 1;
    
    return $prefix . $year . $month . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * توليد رقم مريض فريد
 */
function generatePatientNumber() {
    $prefix = 'P';
    $year = date('Y');
    
    // البحث عن آخر رقم مريض في السنة الحالية
    $query = "SELECT MAX(CAST(SUBSTRING(patient_number, 6) AS UNSIGNED)) as last_number 
              FROM patients 
              WHERE patient_number LIKE ? 
              AND YEAR(created_at) = ?";
    
    $pattern = $prefix . $year . '%';
    $result = fetchOne($query, [$pattern, $year]);
    
    $nextNumber = ($result['last_number'] ?? 0) + 1;
    
    return $prefix . $year . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * التحقق من تعارض المواعيد
 */
function checkAppointmentConflict($doctorId, $date, $time, $duration = 30, $excludeId = null) {
    $startTime = $time;
    $endTime = date('H:i:s', strtotime($time) + ($duration * 60));
    
    $query = "SELECT COUNT(*) as count FROM appointments 
              WHERE doctor_id = ? 
              AND appointment_date = ? 
              AND status NOT IN ('cancelled', 'no_show')
              AND (
                  (appointment_time <= ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL duration MINUTE) > ?) 
                  OR 
                  (appointment_time < ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL duration MINUTE) >= ?)
              )";
    
    $params = [$doctorId, $date, $startTime, $startTime, $endTime, $endTime];
    
    if ($excludeId) {
        $query .= " AND id != ?";
        $params[] = $excludeId;
    }
    
    $result = fetchOne($query, $params);
    return $result['count'] > 0;
}

/**
 * حساب العمر من تاريخ الميلاد
 */
function calculateAge($birthDate) {
    if (empty($birthDate) || $birthDate == '0000-00-00') {
        return null;
    }
    
    $birth = new DateTime($birthDate);
    $today = new DateTime();
    $age = $birth->diff($today);
    
    return $age->y;
}

/**
 * تحويل النص إلى slug للروابط
 */
function createSlug($text) {
    $text = trim($text);
    $text = preg_replace('/[^a-zA-Z0-9\s\u0600-\u06FF]/', '', $text);
    $text = preg_replace('/\s+/', '-', $text);
    return strtolower($text);
}
?>
