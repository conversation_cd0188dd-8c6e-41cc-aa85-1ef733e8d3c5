# نظام حكيم لإدارة العيادات - ملفات مستبعدة من Git
# Hakim Clinic Management System - Git Ignore File

# ملفات الإعدادات الحساسة
config/database.php
config/config.local.php
.env
.env.local
.env.production

# ملفات رفع المستخدمين
uploads/*
!uploads/.htaccess
!uploads/index.php

# ملفات السجلات
logs/*.log
logs/*.txt
error_log
*.log

# ملفات النسخ الاحتياطي
backup/
*.sql
*.sql.gz
*.sql.zip

# ملفات مؤقتة
tmp/
temp/
cache/
*.tmp
*.temp

# ملفات النظام
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.buildpath
.settings/
*.sublime-project
*.sublime-workspace

# ملفات Composer
vendor/
composer.lock
composer.phar

# ملفات Node.js (إذا استخدمت)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ملفات PHP
*.php~
*.php.bak
*.php.orig

# ملفات التثبيت (بعد التثبيت)
install.php
setup.php

# ملفات الاختبار
tests/coverage/
phpunit.xml
.phpunit.result.cache

# ملفات التطوير
docs/build/
*.md~

# ملفات الخادم
.htaccess.bak
.htpasswd

# ملفات قواعد البيانات المحلية
*.sqlite
*.sqlite3
*.db

# ملفات الجلسات
sessions/
sess_*

# ملفات التخزين المؤقت
storage/cache/
storage/logs/
storage/sessions/

# ملفات الأمان
*.key
*.pem
*.crt
*.csr

# ملفات التكوين المحلية
config.local.php
settings.local.php

# ملفات الإحصائيات
stats/
analytics/

# ملفات التصدير
exports/
reports/generated/

# ملفات مضغوطة
*.zip
*.tar.gz
*.rar
*.7z

# ملفات الصور المؤقتة
*.tmp.jpg
*.tmp.png
*.tmp.gif

# ملفات PDF المؤقتة
*.tmp.pdf

# ملفات Excel المؤقتة
~$*.xlsx
~$*.xls

# ملفات Word المؤقتة
~$*.docx
~$*.doc
