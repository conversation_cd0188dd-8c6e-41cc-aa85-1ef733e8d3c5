# حماية مجلد الرفع - نظام حكيم لإدارة العيادات
# Upload Directory Protection - Hakim Clinic Management System

# منع تنفيذ ملفات PHP
<Files "*.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.php3">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "*.php4">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php5">
    Order allow,deny
    Deny from all
</Files>

<Files "*.phtml">
    Order allow,deny
    Deny from all
</Files>

<Files "*.pl">
    Order allow,deny
    Deny from all
</Files>

<Files "*.py">
    Order allow,deny
    Deny from all
</Files>

<Files "*.jsp">
    Order allow,deny
    Deny from all
</Files>

<Files "*.asp">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sh">
    Order allow,deny
    Deny from all
</Files>

<Files "*.cgi">
    Order allow,deny
    Deny from all
</Files>

# منع عرض محتويات المجلد
Options -Indexes

# السماح بأنواع الملفات المحددة فقط
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|txt)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
</IfModule>
