<?php
/**
 * ملف التثبيت - نظام حكيم لإدارة العيادات
 * Installation File - Hakim Clinic Management System
 */

require_once 'config/config.php';

$message = '';
$error = '';

// التحقق من إمكانية الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
        // قراءة ملف SQL
        $sqlFile = 'database/hakim_db.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }
        
        $sql = file_get_contents($sqlFile);
        
        // تنفيذ الاستعلامات
        $pdo->exec($sql);
        
        $message = 'تم تثبيت النظام بنجاح! يمكنك الآن تسجيل الدخول.';
    }
    
} catch (Exception $e) {
    $error = 'خطأ في التثبيت: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام حكيم لإدارة العيادات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #007BFF, #0056b3);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .install-header h1 {
            color: #007BFF;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .btn-install {
            background: linear-gradient(135deg, #007BFF, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <i class="fas fa-stethoscope fa-3x text-primary mb-3"></i>
            <h1>نظام حكيم لإدارة العيادات</h1>
            <p class="text-muted">مرحباً بك في عملية التثبيت</p>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
            </div>
            <div class="text-center">
                <a href="login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>الذهاب لتسجيل الدخول
                </a>
            </div>
        <?php elseif (!empty($error)): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <div class="mb-4">
                <h5>متطلبات النظام:</h5>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        PHP 7.4+
                        <span class="badge bg-success rounded-pill">
                            <i class="fas fa-check"></i>
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        MySQL 5.7+
                        <span class="badge bg-success rounded-pill">
                            <i class="fas fa-check"></i>
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        PDO Extension
                        <span class="badge bg-success rounded-pill">
                            <i class="fas fa-check"></i>
                        </span>
                    </li>
                </ul>
            </div>
            
            <div class="mb-4">
                <h5>إعدادات قاعدة البيانات:</h5>
                <div class="bg-light p-3 rounded">
                    <p><strong>الخادم:</strong> <?php echo DB_HOST; ?></p>
                    <p><strong>اسم قاعدة البيانات:</strong> <?php echo DB_NAME; ?></p>
                    <p><strong>اسم المستخدم:</strong> <?php echo DB_USER; ?></p>
                    <p class="mb-0"><strong>حالة الاتصال:</strong> 
                        <span class="badge bg-success">متصل</span>
                    </p>
                </div>
            </div>
            
            <div class="mb-4">
                <h5>بيانات تسجيل الدخول الافتراضية:</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                <h6>مدير النظام</h6>
                                <small>admin / admin123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                                <h6>طبيب</h6>
                                <small>doctor / doctor123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                <h6>سكرتيرة</h6>
                                <small>secretary / secretary123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="POST" action="">
                <div class="d-grid">
                    <button type="submit" name="install" class="btn btn-primary btn-install">
                        <i class="fas fa-download me-2"></i>بدء التثبيت
                    </button>
                </div>
            </form>
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                نظام حكيم لإدارة العيادات - الإصدار <?php echo SITE_VERSION; ?>
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
