<?php
/**
 * ملف التثبيت المبسط - نظام حكيم لإدارة العيادات
 * Simple Installation File - Hakim Clinic Management System
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'collectandwin2_altarda';
$db_user = 'collectandwin2_altarda';
$db_pass = 'collectandwin2_altarda';

$message = '';
$error = '';
$step = 1;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // الخطوة 1: اختبار الاتصال
        if (isset($_POST['test_connection'])) {
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $message = 'تم الاتصال بالخادم بنجاح!';
            $step = 2;
        }
        
        // الخطوة 2: إنشاء قاعدة البيانات
        elseif (isset($_POST['create_database'])) {
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $message = 'تم إنشاء قاعدة البيانات بنجاح!';
            $step = 3;
        }
        
        // الخطوة 3: إنشاء الجداول
        elseif (isset($_POST['create_tables'])) {
            $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // قراءة ملف SQL المحدث
            $sqlFile = 'database/install_tables.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception('ملف قاعدة البيانات غير موجود');
            }

            $sql = file_get_contents($sqlFile);
            
            // تنفيذ الاستعلامات
            $statements = explode(';', $sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            $message = 'تم إنشاء الجداول وإدراج البيانات بنجاح!';
            $step = 4;
        }
        
    } catch (Exception $e) {
        $error = 'خطأ: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام حكيم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #007BFF, #0056b3); min-height: 100vh; }
        .install-container { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); padding: 3rem; max-width: 600px; margin: 2rem auto; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="text-center mb-4">
            <i class="fas fa-stethoscope fa-3x text-primary mb-3"></i>
            <h1 class="text-primary">تثبيت نظام حكيم</h1>
            <p class="text-muted">معالج التثبيت المبسط</p>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <!-- مؤشر التقدم -->
        <div class="progress mb-4">
            <div class="progress-bar" style="width: <?php echo ($step / 4) * 100; ?>%"></div>
        </div>
        
        <div class="mb-4">
            <h5>إعدادات قاعدة البيانات:</h5>
            <div class="bg-light p-3 rounded">
                <p><strong>الخادم:</strong> <?php echo $db_host; ?></p>
                <p><strong>قاعدة البيانات:</strong> <?php echo $db_name; ?></p>
                <p><strong>المستخدم:</strong> <?php echo $db_user; ?></p>
                <p class="mb-0"><strong>كلمة المرور:</strong> ***مخفية***</p>
            </div>
        </div>
        
        <?php if ($step == 1): ?>
            <h5>الخطوة 1: اختبار الاتصال</h5>
            <p>سنقوم أولاً باختبار الاتصال بخادم قاعدة البيانات.</p>
            <form method="POST">
                <button type="submit" name="test_connection" class="btn btn-primary">
                    <i class="fas fa-plug me-2"></i>اختبار الاتصال
                </button>
            </form>
            
        <?php elseif ($step == 2): ?>
            <h5>الخطوة 2: إنشاء قاعدة البيانات</h5>
            <p>سنقوم الآن بإنشاء قاعدة البيانات.</p>
            <form method="POST">
                <button type="submit" name="create_database" class="btn btn-primary">
                    <i class="fas fa-database me-2"></i>إنشاء قاعدة البيانات
                </button>
            </form>
            
        <?php elseif ($step == 3): ?>
            <h5>الخطوة 3: إنشاء الجداول</h5>
            <p>سنقوم الآن بإنشاء الجداول وإدراج البيانات الأساسية.</p>
            <form method="POST">
                <button type="submit" name="create_tables" class="btn btn-primary">
                    <i class="fas fa-table me-2"></i>إنشاء الجداول
                </button>
            </form>
            
        <?php elseif ($step == 4): ?>
            <div class="alert alert-success text-center">
                <i class="fas fa-check-circle fa-3x mb-3"></i>
                <h4>تم التثبيت بنجاح!</h4>
                <p>يمكنك الآن استخدام النظام.</p>
            </div>
            
            <div class="mb-4">
                <h5>بيانات تسجيل الدخول:</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                <h6>مدير</h6>
                                <small>admin<br>admin123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                                <h6>طبيب</h6>
                                <small>doctor<br>doctor123</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                <h6>سكرتيرة</h6>
                                <small>secretary<br>secretary123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <a href="login.php" class="btn btn-success btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </a>
                <a href="index.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                </a>
            </div>
            
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>مهم:</strong> احذف ملفات التثبيت (install.php و install_simple.php) بعد اكتمال التثبيت لأسباب أمنية.
            </div>
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <small class="text-muted">نظام حكيم لإدارة العيادات - الإصدار 1.0.0</small>
        </div>
    </div>
</body>
</html>
