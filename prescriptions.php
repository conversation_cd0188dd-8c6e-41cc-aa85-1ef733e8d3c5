<?php
/**
 * الوصفات الطبية - نظام حكيم لإدارة العيادات
 * Prescriptions Management - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (الأطباء فقط)
if (!hasPermission('doctor')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    redirect('dashboard.php');
}

$pageTitle = 'الوصفات الطبية';
$action = $_GET['action'] ?? 'list';
$prescriptionId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $data = [
            'patient_id' => intval($_POST['patient_id']),
            'visit_id' => !empty($_POST['visit_id']) ? intval($_POST['visit_id']) : null,
            'prescription_date' => $_POST['prescription_date'],
            'notes' => sanitizeInput($_POST['notes'])
        ];
        
        $medications = $_POST['medications'] ?? [];
        
        // التحقق من صحة البيانات
        $errors = [];
        if (empty($data['patient_id'])) $errors[] = 'يجب اختيار المريض';
        if (empty($data['prescription_date'])) $errors[] = 'تاريخ الوصفة مطلوب';
        if (empty($medications)) $errors[] = 'يجب إضافة دواء واحد على الأقل';
        
        // التحقق من صحة بيانات الأدوية
        foreach ($medications as $index => $med) {
            if (empty($med['medication_name'])) {
                $errors[] = "اسم الدواء مطلوب في السطر " . ($index + 1);
            }
            if (empty($med['dosage'])) {
                $errors[] = "الجرعة مطلوبة في السطر " . ($index + 1);
            }
            if (empty($med['frequency'])) {
                $errors[] = "عدد مرات التناول مطلوب في السطر " . ($index + 1);
            }
            if (empty($med['duration'])) {
                $errors[] = "مدة العلاج مطلوبة في السطر " . ($index + 1);
            }
        }
        
        if (empty($errors)) {
            try {
                if ($action === 'add') {
                    // إضافة وصفة جديدة
                    $data['doctor_id'] = $_SESSION['user_id'];
                    
                    $query = "INSERT INTO prescriptions (patient_id, doctor_id, visit_id, prescription_date, notes) VALUES (?, ?, ?, ?, ?)";
                    $params = [$data['patient_id'], $data['doctor_id'], $data['visit_id'], $data['prescription_date'], $data['notes']];
                    
                    executeQuery($query, $params);
                    $newPrescriptionId = getLastInsertId();
                    
                    // إضافة الأدوية
                    foreach ($medications as $med) {
                        $medQuery = "INSERT INTO prescription_items (prescription_id, medication_id, medication_name, dosage, frequency, duration, instructions) VALUES (?, ?, ?, ?, ?, ?, ?)";
                        $medParams = [
                            $newPrescriptionId,
                            !empty($med['medication_id']) ? $med['medication_id'] : null,
                            $med['medication_name'],
                            $med['dosage'],
                            $med['frequency'],
                            $med['duration'],
                            $med['instructions'] ?? ''
                        ];
                        executeQuery($medQuery, $medParams);
                    }
                    
                    logActivity($_SESSION['user_id'], 'إضافة وصفة طبية جديدة', 'prescriptions', $newPrescriptionId, null, $data);
                    $_SESSION['success_message'] = 'تم إضافة الوصفة الطبية بنجاح';
                    
                } else {
                    // تحديث وصفة موجودة
                    $query = "UPDATE prescriptions SET patient_id=?, visit_id=?, prescription_date=?, notes=? WHERE id=? AND doctor_id=?";
                    $params = [$data['patient_id'], $data['visit_id'], $data['prescription_date'], $data['notes'], $prescriptionId, $_SESSION['user_id']];
                    
                    executeQuery($query, $params);
                    
                    // حذف الأدوية القديمة وإضافة الجديدة
                    executeQuery("DELETE FROM prescription_items WHERE prescription_id = ?", [$prescriptionId]);
                    
                    foreach ($medications as $med) {
                        $medQuery = "INSERT INTO prescription_items (prescription_id, medication_id, medication_name, dosage, frequency, duration, instructions) VALUES (?, ?, ?, ?, ?, ?, ?)";
                        $medParams = [
                            $prescriptionId,
                            !empty($med['medication_id']) ? $med['medication_id'] : null,
                            $med['medication_name'],
                            $med['dosage'],
                            $med['frequency'],
                            $med['duration'],
                            $med['instructions'] ?? ''
                        ];
                        executeQuery($medQuery, $medParams);
                    }
                    
                    logActivity($_SESSION['user_id'], 'تحديث وصفة طبية', 'prescriptions', $prescriptionId, null, $data);
                    $_SESSION['success_message'] = 'تم تحديث الوصفة الطبية بنجاح';
                }
                
                redirect('prescriptions.php');
                
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    }
}

// حذف وصفة
if ($action === 'delete' && $prescriptionId) {
    try {
        // التحقق من أن الوصفة تخص الطبيب الحالي أو المدير
        $checkQuery = "SELECT id FROM prescriptions WHERE id = ? AND (doctor_id = ? OR ? = 'admin')";
        $checkResult = fetchOne($checkQuery, [$prescriptionId, $_SESSION['user_id'], $_SESSION['user_type']]);
        
        if ($checkResult) {
            $query = "DELETE FROM prescriptions WHERE id = ?";
            executeQuery($query, [$prescriptionId]);
            
            logActivity($_SESSION['user_id'], 'حذف وصفة طبية', 'prescriptions', $prescriptionId);
            $_SESSION['success_message'] = 'تم حذف الوصفة الطبية بنجاح';
        } else {
            $_SESSION['error_message'] = 'ليس لديك صلاحية لحذف هذه الوصفة';
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ في الحذف: ' . $e->getMessage();
    }
    
    redirect('prescriptions.php');
}

// جلب بيانات الوصفة للتحرير
$prescription = null;
$prescriptionItems = [];
if ($action === 'edit' && $prescriptionId) {
    $query = "SELECT * FROM prescriptions WHERE id = ? AND (doctor_id = ? OR ? = 'admin')";
    $prescription = fetchOne($query, [$prescriptionId, $_SESSION['user_id'], $_SESSION['user_type']]);
    
    if (!$prescription) {
        $_SESSION['error_message'] = 'الوصفة غير موجودة أو ليس لديك صلاحية للوصول إليها';
        redirect('prescriptions.php');
    }
    
    // جلب أدوية الوصفة
    $itemsQuery = "SELECT * FROM prescription_items WHERE prescription_id = ?";
    $prescriptionItems = fetchAll($itemsQuery, [$prescriptionId]);
}

// جلب قائمة المرضى
$patientsQuery = "SELECT id, CONCAT(first_name, ' ', last_name) as full_name, phone, patient_number FROM patients ORDER BY first_name";
$patients = fetchAll($patientsQuery);

// جلب قائمة الأدوية
$medicationsQuery = "SELECT * FROM medications WHERE is_active = 1 ORDER BY name";
$medications = fetchAll($medicationsQuery);

// جلب قائمة الوصفات
$searchTerm = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$whereClause = "WHERE (p.doctor_id = ? OR ? = 'admin')";
$params = [$_SESSION['user_id'], $_SESSION['user_type']];

if (!empty($searchTerm)) {
    $whereClause .= " AND (CONCAT(pt.first_name, ' ', pt.last_name) LIKE ? OR pt.patient_number LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
}

$countQuery = "SELECT COUNT(*) as total FROM prescriptions p JOIN patients pt ON p.patient_id = pt.id $whereClause";
$totalPrescriptions = fetchOne($countQuery, $params)['total'];
$totalPages = ceil($totalPrescriptions / $limit);

$prescriptionsQuery = "
    SELECT p.*, 
           CONCAT(pt.first_name, ' ', pt.last_name) as patient_name,
           pt.patient_number,
           u.full_name as doctor_name
    FROM prescriptions p
    JOIN patients pt ON p.patient_id = pt.id
    JOIN users u ON p.doctor_id = u.id
    $whereClause
    ORDER BY p.prescription_date DESC, p.created_at DESC
    LIMIT $limit OFFSET $offset
";
$prescriptionsList = fetchAll($prescriptionsQuery, $params);

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- قائمة الوصفات -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-prescription-bottle-alt me-2"></i>الوصفات الطبية</h2>
    <a href="prescriptions.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>كتابة وصفة جديدة
    </a>
</div>

<!-- شريط البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" name="search" 
                       placeholder="البحث بالمريض أو رقم المريض..." 
                       value="<?php echo htmlspecialchars($searchTerm); ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="prescriptions.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول الوصفات -->
<div class="card">
    <div class="card-body">
        <?php if (empty($prescriptionsList)): ?>
            <div class="text-center py-5">
                <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وصفات طبية</h5>
                <p class="text-muted">ابدأ بكتابة وصفة طبية جديدة</p>
                <a href="prescriptions.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>كتابة وصفة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المريض</th>
                            <th>الطبيب</th>
                            <th>عدد الأدوية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prescriptionsList as $presc): ?>
                        <tr class="searchable-row">
                            <td><?php echo formatDateForDisplay($presc['prescription_date']); ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($presc['patient_name']); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo htmlspecialchars($presc['patient_number']); ?></small>
                            </td>
                            <td><?php echo htmlspecialchars($presc['doctor_name']); ?></td>
                            <td>
                                <?php
                                $itemsCount = fetchOne("SELECT COUNT(*) as count FROM prescription_items WHERE prescription_id = ?", [$presc['id']])['count'];
                                echo $itemsCount . ' دواء';
                                ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="prescriptions.php?action=view&id=<?php echo $presc['id']; ?>" 
                                       class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="prescriptions.php?action=print&id=<?php echo $presc['id']; ?>" 
                                       class="btn btn-sm btn-outline-success" title="طباعة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <?php if ($presc['doctor_id'] == $_SESSION['user_id'] || $_SESSION['user_type'] === 'admin'): ?>
                                    <a href="prescriptions.php?action=edit&id=<?php echo $presc['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="prescriptions.php?action=delete&id=<?php echo $presc['id']; ?>" 
                                       class="btn btn-sm btn-outline-danger btn-delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تحرير وصفة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-prescription-bottle-<?php echo $action === 'add' ? 'alt' : 'medical'; ?> me-2"></i>
        <?php echo $action === 'add' ? 'كتابة وصفة طبية جديدة' : 'تحرير الوصفة الطبية'; ?>
    </h2>
    <a href="prescriptions.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="patient_id" class="form-label">المريض *</label>
                    <select class="form-control" id="patient_id" name="patient_id" required>
                        <option value="">اختر المريض</option>
                        <?php foreach ($patients as $patient): ?>
                        <option value="<?php echo $patient['id']; ?>" 
                                <?php echo ($prescription['patient_id'] ?? $_GET['patient_id'] ?? '') == $patient['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($patient['full_name'] . ' - ' . $patient['patient_number']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-6">
                    <label for="prescription_date" class="form-label">تاريخ الوصفة *</label>
                    <input type="date" class="form-control" id="prescription_date" name="prescription_date" 
                           value="<?php echo $prescription['prescription_date'] ?? date('Y-m-d'); ?>" required>
                </div>
            </div>
            
            <!-- قائمة الأدوية -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-pills me-2"></i>الأدوية</h5>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addMedicationRow()">
                        <i class="fas fa-plus me-1"></i>إضافة دواء
                    </button>
                </div>
                
                <div id="medications-container">
                    <?php if (!empty($prescriptionItems)): ?>
                        <?php foreach ($prescriptionItems as $index => $item): ?>
                        <div class="medication-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">اسم الدواء *</label>
                                    <input type="text" class="form-control medication-name" 
                                           name="medications[<?php echo $index; ?>][medication_name]" 
                                           value="<?php echo htmlspecialchars($item['medication_name']); ?>" 
                                           list="medications-list" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الجرعة *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[<?php echo $index; ?>][dosage]" 
                                           value="<?php echo htmlspecialchars($item['dosage']); ?>" 
                                           placeholder="مثل: 500mg" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">عدد المرات *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[<?php echo $index; ?>][frequency]" 
                                           value="<?php echo htmlspecialchars($item['frequency']); ?>" 
                                           placeholder="مثل: 3 مرات يومياً" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">المدة *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[<?php echo $index; ?>][duration]" 
                                           value="<?php echo htmlspecialchars($item['duration']); ?>" 
                                           placeholder="مثل: 7 أيام" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">تعليمات</label>
                                    <input type="text" class="form-control" 
                                           name="medications[<?php echo $index; ?>][instructions]" 
                                           value="<?php echo htmlspecialchars($item['instructions']); ?>" 
                                           placeholder="بعد الأكل">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMedicationRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- صف فارغ للبداية -->
                        <div class="medication-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">اسم الدواء *</label>
                                    <input type="text" class="form-control medication-name" 
                                           name="medications[0][medication_name]" 
                                           list="medications-list" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الجرعة *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[0][dosage]" 
                                           placeholder="مثل: 500mg" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">عدد المرات *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[0][frequency]" 
                                           placeholder="مثل: 3 مرات يومياً" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">المدة *</label>
                                    <input type="text" class="form-control" 
                                           name="medications[0][duration]" 
                                           placeholder="مثل: 7 أيام" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">تعليمات</label>
                                    <input type="text" class="form-control" 
                                           name="medications[0][instructions]" 
                                           placeholder="بعد الأكل">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMedicationRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات إضافية</label>
                <textarea class="form-control" id="notes" name="notes" rows="3" 
                          placeholder="أي ملاحظات أو تعليمات إضافية..."><?php echo htmlspecialchars($prescription['notes'] ?? ''); ?></textarea>
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    <?php echo $action === 'add' ? 'حفظ الوصفة' : 'حفظ التغييرات'; ?>
                </button>
                <a href="prescriptions.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الأدوية للاقتراحات -->
<datalist id="medications-list">
    <?php foreach ($medications as $med): ?>
    <option value="<?php echo htmlspecialchars($med['name']); ?>">
        <?php echo htmlspecialchars($med['generic_name'] . ' - ' . $med['strength']); ?>
    </option>
    <?php endforeach; ?>
</datalist>

<?php endif; ?>

<?php
$inlineJS = "
let medicationIndex = " . (count($prescriptionItems) > 0 ? count($prescriptionItems) : 1) . ";

function addMedicationRow() {
    const container = document.getElementById('medications-container');
    const newRow = document.createElement('div');
    newRow.className = 'medication-row border rounded p-3 mb-3';
    newRow.innerHTML = `
        <div class='row'>
            <div class='col-md-3'>
                <label class='form-label'>اسم الدواء *</label>
                <input type='text' class='form-control medication-name' 
                       name='medications[\${medicationIndex}][medication_name]' 
                       list='medications-list' required>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>الجرعة *</label>
                <input type='text' class='form-control' 
                       name='medications[\${medicationIndex}][dosage]' 
                       placeholder='مثل: 500mg' required>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>عدد المرات *</label>
                <input type='text' class='form-control' 
                       name='medications[\${medicationIndex}][frequency]' 
                       placeholder='مثل: 3 مرات يومياً' required>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>المدة *</label>
                <input type='text' class='form-control' 
                       name='medications[\${medicationIndex}][duration]' 
                       placeholder='مثل: 7 أيام' required>
            </div>
            <div class='col-md-2'>
                <label class='form-label'>تعليمات</label>
                <input type='text' class='form-control' 
                       name='medications[\${medicationIndex}][instructions]' 
                       placeholder='بعد الأكل'>
            </div>
            <div class='col-md-1 d-flex align-items-end'>
                <button type='button' class='btn btn-outline-danger btn-sm' onclick='removeMedicationRow(this)'>
                    <i class='fas fa-trash'></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newRow);
    medicationIndex++;
}

function removeMedicationRow(button) {
    const container = document.getElementById('medications-container');
    if (container.children.length > 1) {
        button.closest('.medication-row').remove();
    } else {
        alert('يجب أن تحتوي الوصفة على دواء واحد على الأقل');
    }
}

// تحديث قائمة المرضى عند البحث
$('#patient_id').select2({
    placeholder: 'ابحث عن المريض...',
    allowClear: true
});
";

include 'includes/footer.php';
?>
