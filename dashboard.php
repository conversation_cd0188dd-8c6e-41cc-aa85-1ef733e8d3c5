<?php
/**
 * لوحة التحكم الرئيسية - نظام حكيم لإدارة العيادات
 * Main Dashboard - Hakim Clinic Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

$pageTitle = 'لوحة التحكم';
$pageActions = '<button class="btn btn-outline-primary" onclick="location.reload()"><i class="fas fa-sync-alt me-2"></i>تحديث</button>';

// جلب الإحصائيات
try {
    // إحصائيات المرضى
    $totalPatientsQuery = "SELECT COUNT(*) as count FROM patients";
    $totalPatients = fetchOne($totalPatientsQuery)['count'];
    
    $newPatientsQuery = "SELECT COUNT(*) as count FROM patients WHERE DATE(created_at) = CURDATE()";
    $newPatientsToday = fetchOne($newPatientsQuery)['count'];
    
    // إحصائيات المواعيد
    $todayAppointmentsQuery = "SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE()";
    $todayAppointments = fetchOne($todayAppointmentsQuery)['count'];
    
    $pendingAppointmentsQuery = "SELECT COUNT(*) as count FROM appointments WHERE appointment_date >= CURDATE() AND status = 'scheduled'";
    $pendingAppointments = fetchOne($pendingAppointmentsQuery)['count'];
    
    $completedAppointmentsQuery = "SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE() AND status = 'completed'";
    $completedAppointments = fetchOne($completedAppointmentsQuery)['count'];
    
    // إحصائيات مالية
    $todayRevenueQuery = "SELECT COALESCE(SUM(total_amount), 0) as revenue FROM billing WHERE DATE(invoice_date) = CURDATE() AND status = 'paid'";
    $todayRevenue = fetchOne($todayRevenueQuery)['revenue'];
    
    $monthRevenueQuery = "SELECT COALESCE(SUM(total_amount), 0) as revenue FROM billing WHERE YEAR(invoice_date) = YEAR(CURDATE()) AND MONTH(invoice_date) = MONTH(CURDATE()) AND status = 'paid'";
    $monthRevenue = fetchOne($monthRevenueQuery)['revenue'];
    
    $pendingPaymentsQuery = "SELECT COALESCE(SUM(total_amount - paid_amount), 0) as pending FROM billing WHERE status IN ('pending', 'partial')";
    $pendingPayments = fetchOne($pendingPaymentsQuery)['pending'];
    
    // المواعيد القادمة
    $upcomingAppointmentsQuery = "
        SELECT a.*, p.first_name, p.last_name, p.phone, u.full_name as doctor_name
        FROM appointments a
        JOIN patients p ON a.patient_id = p.id
        JOIN users u ON a.doctor_id = u.id
        WHERE a.appointment_date >= CURDATE() 
        AND a.status IN ('scheduled', 'confirmed')
        ORDER BY a.appointment_date, a.appointment_time
        LIMIT 10
    ";
    $upcomingAppointments = fetchAll($upcomingAppointmentsQuery);
    
    // المرضى الجدد
    $recentPatientsQuery = "
        SELECT * FROM patients 
        ORDER BY created_at DESC 
        LIMIT 5
    ";
    $recentPatients = fetchAll($recentPatientsQuery);
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $totalPatients = $newPatientsToday = $todayAppointments = $pendingAppointments = 0;
    $completedAppointments = $todayRevenue = $monthRevenue = $pendingPayments = 0;
    $upcomingAppointments = $recentPatients = [];
}

include 'includes/header.php';
?>

<!-- بطاقات الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المرضى
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($totalPatients); ?>
                        </div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-plus text-success me-1"></i>
                            <?php echo $newPatientsToday; ?> جديد اليوم
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            مواعيد اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($todayAppointments); ?>
                        </div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-check text-success me-1"></i>
                            <?php echo $completedAppointments; ?> مكتمل
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إيرادات اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatCurrency($todayRevenue); ?>
                        </div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-calendar-alt text-info me-1"></i>
                            <?php echo formatCurrency($monthRevenue); ?> هذا الشهر
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            مواعيد معلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($pendingAppointments); ?>
                        </div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                            <?php echo formatCurrency($pendingPayments); ?> مدفوعات معلقة
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المواعيد القادمة -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-calendar-alt me-2"></i>
                    المواعيد القادمة
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($upcomingAppointments)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد قادمة</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المريض</th>
                                    <th>الطبيب</th>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                    <th>الهاتف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingAppointments as $appointment): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($appointment['doctor_name']); ?></td>
                                    <td><?php echo formatDateForDisplay($appointment['appointment_date']); ?></td>
                                    <td><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($appointment['status']) {
                                            case 'scheduled':
                                                $statusClass = 'warning';
                                                $statusText = 'مجدول';
                                                break;
                                            case 'confirmed':
                                                $statusClass = 'info';
                                                $statusText = 'مؤكد';
                                                break;
                                            case 'completed':
                                                $statusClass = 'success';
                                                $statusText = 'مكتمل';
                                                break;
                                            default:
                                                $statusClass = 'secondary';
                                                $statusText = $appointment['status'];
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo $appointment['phone']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($appointment['phone']); ?>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="appointments.php" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>عرض جميع المواعيد
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- المرضى الجدد -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-user-plus me-2"></i>
                    المرضى الجدد
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentPatients)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مرضى جدد</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($recentPatients as $patient): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">
                                <?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo formatDateForDisplay($patient['created_at'], 'd/m/Y H:i'); ?>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <a href="patients.php?view=<?php echo $patient['id']; ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-3">
                        <a href="patients.php" class="btn btn-outline-success">
                            <i class="fas fa-users me-2"></i>عرض جميع المرضى
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="patients.php?action=add" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>إضافة مريض جديد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="appointments.php?action=add" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                            <span>حجز موعد جديد</span>
                        </a>
                    </div>
                    <?php if (hasPermission('doctor')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="prescriptions.php?action=add" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-prescription-bottle-alt fa-2x mb-2"></i>
                            <span>كتابة وصفة طبية</span>
                        </a>
                    </div>
                    <?php endif; ?>
                    <div class="col-md-3 mb-3">
                        <a href="billing.php?action=add" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-file-invoice fa-2x mb-2"></i>
                            <span>إنشاء فاتورة</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$inlineJS = "
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة AJAX لتحديث الإحصائيات دون إعادة تحميل الصفحة
    }, 30000);
";

include 'includes/footer.php';
?>
